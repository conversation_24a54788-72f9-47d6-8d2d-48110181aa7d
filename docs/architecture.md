# Stack Advantage Architecture Overview

This document provides a high-level overview of the Stack Advantage Blackjack Trainer architecture, explaining how different modules interact and the design patterns used.

## Architecture Overview

Stack Advantage follows a modular architecture with clear separation of concerns:

```
┌─────────────────────┐      ┌─────────────────────┐
│     Components      │      │       Hooks         │
│  (React/Next.js UI) │◄────►│  (State Management) │
└─────────┬───────────┘      └─────────┬───────────┘
          │                            │
          │                            │
          ▼                            ▼
┌─────────────────────┐      ┌─────────────────────┐
│  Game Logic (lib)   │◄────►│    AI Integration   │
│ (Core Game Engine)  │      │  (Strategy Hints)   │
└─────────────────────┘      └─────────────────────┘
```

### Key Architectural Components

1. **Game Logic (Core Engine)**
   - Located in `src/lib/game/blackjack.ts`
   - Implements the `BlackjackGame` class that encapsulates all game rules, state, and actions
   - Handles deck management, hand calculation, game actions (hit, stand, etc.)
   - Manages card counting and true count calculations

2. **React Hooks (State Management)**
   - Located in `src/hooks/`
   - Primary hooks:
     - `useBlackjack`: Main hook for managing game state and actions
     - `useGameSettings`: Manages game configuration 
     - `useStrategyHint`: Interfaces with AI for strategy recommendations
     - `usePerformanceMetrics`: Tracks player performance statistics

3. **UI Components**
   - Located in `src/components/`
   - Organized into:
     - Game-specific components (`game/`)
     - General UI components (`ui/`) using shadcn/ui

4. **AI Integration**
   - Located in `src/ai/`
   - Uses Google AI via Genkit to provide strategy hints
   - Implementation in `src/ai/flows/blackjack-strategy-hint.ts`

## Design Patterns

The application uses several key design patterns:

1. **Model-View-Controller (MVC)**
   - Model: `BlackjackGame` class and associated types
   - View: React components in `src/components/`
   - Controller: React hooks that mediate between the model and view

2. **Custom Hooks Pattern**
   - Encapsulates complex logic and state in reusable React hooks
   - Separates UI from business logic

3. **Service Layer Pattern**
   - AI integration is implemented as a service layer accessible via hooks
   - Game logic is encapsulated in a service-like class

4. **Stateless/Functional Component Pattern**
   - UI components are primarily functional components
   - State is managed via hooks, not in the components themselves

## Data Flow

1. User interacts with UI components
2. Components call hook methods (e.g., `hit()`, `stand()`)
3. Hooks call methods on the `BlackjackGame` instance
4. Game logic updates its internal state
5. Updated state is returned to hooks
6. Hooks update React state, triggering UI re-renders
7. For strategy hints, the AI service is called based on the current game state

## State Management

Stack Advantage uses React's built-in state management capabilities rather than external libraries like Redux or Zustand:

- Local component state for UI-specific concerns
- Context/hooks for sharing state across components
- The `BlackjackGame` class maintains the authoritative game state

This approach provides a good balance of simplicity and functionality for this application's needs.

## Error Handling

- The core game logic includes extensive validation for all actions
- Actions return a structured `ActionResult` type with success/error information
- AI services include fallback mechanisms for when AI services are unavailable

## Future Architectural Considerations

- Consider implementing a more robust state management solution if the application grows more complex
- Potential migration to a more formal state machine pattern
- Expansion of the AI system to handle more advanced strategy training scenarios