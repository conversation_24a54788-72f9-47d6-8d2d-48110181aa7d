# Game Logic Documentation

This document details the core Blackjack game logic implemented in the Stack Advantage trainer.

## Files

*   `src/app/page.tsx`: Currently contains most of the game logic, state management, and UI rendering for the main game page. (Note: Logic should ideally be refactored into `src/lib/game/blackjack.ts`).
*   `src/lib/game/blackjack.ts`: Intended location for the core `BlackjackGame` class to encapsulate game state and rules. Currently contains a placeholder class structure.

## Core Functions (from `page.tsx`)

*   **`createDeck(numberOfDecks: number = 1): string[]`**
    *   **Purpose:** Generates one or more standard 52-card decks.
    *   **Parameters:** `numberOfDecks` (number, default: 1) - How many decks to create.
    *   **Returns:** An array of strings representing cards (e.g., "HA" for Ace of Hearts).

*   **`shuffleDeck(deck: string[]): string[]`**
    *   **Purpose:** Shuffles an array of cards using the Fisher-Yates (Knuth) algorithm.
    *   **Parameters:** `deck` (string[]) - The array of card strings to shuffle.
    *   **Returns:** A new array containing the shuffled cards.

*   **`getCardValue(card: string): number`**
    *   **Purpose:** Calculates the Blackjack value of a card string.
    *   **Parameters:** `card` (string) - The card identifier (e.g., "SK", "DA").
    *   **Returns:** The numerical value (Ace=11, J/Q/K=10, others face value).

*   **`calculateHandTotal(hand: string[]): { total: number; isSoft: boolean }`**
    *   **Purpose:** Calculates the total value of a hand, accounting for Aces (soft/hard totals).
    *   **Parameters:** `hand` (string[]) - Array of card strings in the hand.
    *   **Returns:** An object with `total` (number) and `isSoft` (boolean - true if an Ace is counted as 11).

*   **`placeBetAndDeal()`**
    *   **Purpose:** Initializes a new round. Validates the bet, shuffles decks, deals initial hands (2 cards each to player and dealer), and updates game state.
    *   **Side Effects:** Modifies `deck`, `playerHand`, `dealerHand`, `gameState`, and other state variables.

*   **`hit()`**
    *   **Purpose:** Handles the player's 'Hit' action. Deals one card to the player, updates totals. If the player busts (>21), automatically triggers the `stand()` logic.
    *   **Side Effects:** Modifies `deck`, `playerHand`, `playerTotal`, `playerIsSoft`. May trigger `stand()`.

*   **`stand()`**
    *   **Purpose:** Handles the player's 'Stand' action. Finalizes the player's hand and plays the dealer's turn according to standard rules (hit until >= 17). Determines the game result.
    *   **Side Effects:** Modifies `deck`, `dealerHand`, `dealerTotal`, `dealerIsSoft`, `gameState`, `gameResult`, `stackSize`.

*   **`doubleDown()`**
    *   **Purpose:** Handles the player's 'Double Down' action. Doubles the bet, deals one card, and immediately stands.
    *   **Conditions:** Only available on the first two cards if the player has enough stack.
    *   **Side Effects:** Modifies `betAmount`, `deck`, `playerHand`, `playerTotal`, `playerIsSoft`. Triggers `stand()`.

*   **`split()`**
    *   **Purpose:** Placeholder for the 'Split' action.
    *   **Conditions:** Only available if the first two cards have the same value and the player has enough stack.
    *   **Note:** Full implementation is pending.

*   **`determineGameResult(finalPlayerTotal: number, finalDealerTotal: number)`**
    *   **Purpose:** Compares final player and dealer totals to determine the outcome (Win, Lose, Push, Blackjack) and calculates winnings/losses.
    *   **Parameters:** `finalPlayerTotal` (number), `finalDealerTotal` (number).
    *   **Side Effects:** Modifies `gameResult`, `stackSize`.

*   **`endGame(finalPlayerTotal: number, finalDealerTotal: number)`**
    *   **Purpose:** Sets the game state to 'ended' after determining the result.
    *   **Parameters:** `finalPlayerTotal` (number), `finalDealerTotal` (number).
    *   **Side Effects:** Modifies `gameState`.

## `BlackjackGame` Class (from `src/lib/game/blackjack.ts`)

*   **Purpose:** Intended to encapsulate all core game logic, state, and rules, separating it from the UI component (`page.tsx`).
*   **Status:** Currently a placeholder structure.
*   **Methods (Planned):**
    *   `constructor(numberOfDecks)`: Initialize game.
    *   `createDeck()`: Manage deck creation/shuffling.
    *   `dealInitialHands()`: Deal starting cards.
    *   `hit()`: Player hits.
    *   `stand()`: Player stands, dealer plays.
    *   `doubleDown()`: Player doubles down.
    *   `split()`: Player splits (future).
    *   `getGameState()`: Return current state (hands, totals, deck status, etc.).
    *   `calculateHandTotal()`: Utility within the class.
    *   `getCardValue()`: Utility within the class.

## Data Structures

*   **Deck:** `string[]` - An array of strings, e.g., `["HA", "C2", "SK", ...]`. Card format is Suit (H, D, C, S) + Value (A, 2-10, J, Q, K).
*   **Hand:** `string[]` - An array representing cards held by player or dealer.
*   **Game State:** Managed via React `useState` hooks in `page.tsx`, including `deck`, `playerHand`, `dealerHand`, `playerTotal`, `dealerTotal`, `gameState` ('betting', 'playing', 'ended'), `stackSize`, `betAmount`, etc.

## Future Refactoring

The primary goal is to move all functions related to deck management, hand calculations, and game actions (hit, stand, etc.) from `page.tsx` into the `BlackjackGame` class in `src/lib/game/blackjack.ts`. The `page.tsx` component would then instantiate and interact with this class, simplifying the component's responsibilities to UI rendering and state updates based on game events.
