# AI System Documentation

This document describes the AI integration within the Stack Advantage application, primarily focused on providing Blackjack strategy hints.

## Files

*   `src/ai/ai-instance.ts`: Configures and exports the Genkit AI instance, likely setting up the connection to the AI model provider (e.g., Google AI).
*   `src/ai/dev.ts`: Used for running the Genkit development server, allowing local testing and debugging of AI flows.
*   `src/ai/flows/blackjack-strategy-hint.ts`: Defines the core Genkit flow for generating Blackjack basic strategy advice.

## Core Components

### 1. AI Instance (`ai-instance.ts`)

*   **Purpose:** Initializes the connection to the underlying AI model (e.g., Google's Gemini via `@genkit-ai/googleai`).
*   **Exports:** An `ai` object used by prompts and flows.
*   **Configuration:** Likely includes API keys (managed via environment variables or Genkit configuration) and potentially model selection (e.g., `gemini-1.5-flash`).

### 2. Genkit Development Server (`dev.ts`)

*   **Purpose:** Provides a local server environment to run and test Genkit flows independently of the main Next.js application.
*   **Usage:** Run via `npm run genkit:dev` or `yarn genkit:dev`.
*   **Functionality:** Allows invoking defined flows (like `blackjackStrategyHintFlow`) via a UI or API for debugging.

### 3. Blackjack Strategy Hint Flow (`blackjack-strategy-hint.ts`)

*   **Purpose:** Generates basic strategy advice (Hit, Stand, Double, Split) based on the player's hand and the dealer's upcard.
*   **Framework:** Uses Genkit (`genkit`, `@genkit-ai/next`).
*   **Key Exports:**
    *   `blackjackStrategyHint(input: BlackjackStrategyHintInput): Promise<BlackjackStrategyHintOutput>`: A server action function callable from the client-side React components. It acts as an interface to the underlying Genkit flow.
*   **Internal Components:**
    *   `BlackjackStrategyHintInputSchema` / `BlackjackStrategyHintInput`: Zod schema and TypeScript type defining the expected input (player hand values, dealer upcard value, soft hand indicator).
    *   `BlackjackStrategyHintOutputSchema` / `BlackjackStrategyHintOutput`: Zod schema and TypeScript type defining the structured output (the recommended `advice` and the `reason`).
    *   `prompt`: A Genkit prompt definition (`ai.definePrompt`) containing:
        *   Input/Output schemas.
        *   The prompt template instructing the AI model on how to behave (act as a Blackjack expert, use basic strategy, format the output as JSON).
    *   `blackjackStrategyHintFlow`: The main Genkit flow definition (`ai.defineFlow`) that:
        *   Takes the structured input.
        *   Calls the `prompt` with the input.
        *   Receives the structured output from the AI model.
        *   Returns the output.
*   **Execution:** Marked with `'use server'`, indicating this code runs on the server-side when called from the client.

## Data Flow for AI Hint

1.  **Client (`page.tsx`):** The `getAiHint` function gathers the current player hand total, dealer upcard value, and soft hand status.
2.  **Client (`page.tsx`):** It calls the `blackjackStrategyHint` server action (imported from `@/ai/flows/blackjack-strategy-hint`).
3.  **Server (`blackjack-strategy-hint.ts`):** The `blackjackStrategyHint` function receives the input.
4.  **Server (`blackjack-strategy-hint.ts`):** It invokes the `blackjackStrategyHintFlow`.
5.  **Server (Genkit):** The flow executes the `prompt` using the configured AI model.
6.  **AI Model:** Processes the prompt and input data, generating the advice and reason based on basic strategy rules.
7.  **Server (Genkit):** The flow receives the structured JSON output from the model.
8.  **Server (`blackjack-strategy-hint.ts`):** The flow returns the output to the `blackjackStrategyHint` function.
9.  **Server (`blackjack-strategy-hint.ts`):** The function returns the result promise to the client.
10. **Client (`page.tsx`):** The `getAiHint` function receives the hint (`advice` and `reason`) and updates the component's state (`aiHint`) to display it in the UI.
