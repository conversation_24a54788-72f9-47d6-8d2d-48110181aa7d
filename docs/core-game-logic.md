# Core Game Logic Documentation

This document provides detailed documentation for the core game logic that powers the Stack Advantage Blackjack Trainer.

## BlackjackGame Class

The `BlackjackGame` class in `src/lib/game/blackjack.ts` is the central game engine that handles all Blackjack game mechanics.

### Properties

| Property | Type | Description |
|----------|------|-------------|
| `deck` | `Card[]` | The current deck of cards in play |
| `settings` | `GameSettings` | Configuration settings for the game |
| `state` | `GameState` | Current state of the game |
| `runningCount` | `number` | Current running count for card counting |

### Methods

#### Constructor

```typescript
constructor(params: GameInitParams = {})
```

Initializes a new game of Blackjack with optional configuration parameters.
- Creates a new shuffled deck
- Initializes the game state
- Sets up game settings (deck count, rules, etc.)

#### Game Actions

| Method | Parameters | Return Type | Description |
|--------|------------|-------------|-------------|
| `placeBet` | `betAmount: number` | `ActionResult` | Places a bet and deals initial cards |
| `hit` | none | `ActionResult` | Draws a card for the active player hand |
| `stand` | none | `ActionResult` | Ends the player's turn and plays dealer hand |
| `doubleDown` | none | `ActionResult` | Doubles the bet, deals one card, and stands |
| `split` | none | `ActionResult` | Splits a pair into two separate hands |
| `shuffleDeck` | none | `boolean` | Creates a new shuffled deck |

#### Helper Methods

| Method | Description |
|--------|-------------|
| `getCardValue(card: Card)` | Gets the Blackjack value of a card (private) |
| `calculateHandTotal(cards: Card[])` | Calculates the total value of a hand, handling Aces (private) |
| `updateCount(card: Card)` | Updates the running count based on the card value (private) |
| `drawCard()` | Draws a card from the deck and updates the count (private) |
| `isBlackjack(cards: Card[])` | Checks if a hand is a blackjack (private) |
| `getGameState()` | Returns a copy of the current game state |
| `getGameResult()` | Returns detailed information about a completed game |
| `getRecommendedBet()` | Calculates the recommended bet based on true count |
| `shouldShuffle()` | Checks if the deck should be shuffled based on penetration |

### Game State Management

The `BlackjackGame` class maintains all game state internally and provides access to it through the `getGameState()` method. This ensures that components cannot directly modify the game state, maintaining data integrity.

### Card Counting Implementation

1. **Running Count**: Tracks the current count based on the card counting system (default: Hi-Lo)
2. **True Count**: Calculates true count by dividing running count by decks remaining
3. **Count Updates**: The count is updated automatically whenever a card is drawn
4. **Deck Penetration**: Game tracks cards remaining and can recommend when to shuffle

### Error Handling

All public methods return an `ActionResult` object with:
- `success`: Boolean indicating if the action was successful
- `error`: Error message if the action failed (optional)
- `gameState`: Current game state after the action

## Types

The game uses several TypeScript interfaces and types defined in `src/types/game.ts`:

### Key Types

| Type/Interface | Description |
|----------------|-------------|
| `Card` | String representation of a card (e.g., "HA" for Ace of Hearts) |
| `GameSettings` | Configuration options for the game |
| `GameState` | Current state of the game including hands, bankroll, count |
| `PlayerHand` | Information about a player's hand including cards and bet |
| `DealerHand` | Information about the dealer's hand |
| `ActionResult` | Result of a game action |
| `HandCalculation` | Result of calculating a hand's value |
| `GameResult` | Result information for a completed game |
| `GameInitParams` | Parameters for initializing a new game |

## Constants

Game constants are defined in `src/lib/constants.ts`:

| Constant | Description |
|----------|-------------|
| `GAME_CONSTANTS` | Basic game configuration (suits, values, etc.) |
| `CARD_VALUES` | Numerical values for each card |
| `COUNTING_SYSTEMS` | Definitions of different card counting systems |
| `DEFAULT_GAME_SETTINGS` | Default settings for a new game |
| `PLAYER_ACTIONS` | Available player actions |
| `GAME_STATES` | Possible game states |

## Usage Examples

### Creating a New Game

```typescript
import { BlackjackGame } from '@/lib/game/blackjack';

// Create a game with default settings
const game = new BlackjackGame();

// Create a game with custom settings
const game = new BlackjackGame({
  settings: {
    deckCount: 4,
    dealerStandsOnSoft17: false,
    doubleAfterSplit: true
  },
  initialBankroll: 2000
});
```

### Game Flow Example

```typescript
// 1. Place a bet to start the game
const betResult = game.placeBet(25);
if (!betResult.success) {
  console.error(betResult.error);
}

// 2. Player decides to hit
const hitResult = game.hit();

// 3. Player decides to stand
const standResult = game.stand();

// 4. Get the result of the game
const gameState = game.getGameState();
console.log(gameState.outcome, gameState.resultMessage);

// 5. Start a new round
game.placeBet(50);
```