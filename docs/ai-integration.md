# AI Integration Documentation

This document describes the AI integration features in Stack Advantage Blackjack Trainer, which provide strategy hints and guidance to players.

## Overview

Stack Advantage integrates Google AI via Genkit to provide intelligent strategy recommendations. The AI system analyzes the current game state and generates strategy hints that consider both standard Blackjack basic strategy and advanced concepts like card counting implications.

## Architecture

```
┌─────────────────┐         ┌───────────────────┐         ┌────────────────┐
│  React UI       │         │  useStrategyHint  │         │  Google AI     │
│  Components     │◄────────┤  Hook             │◄────────┤  (via Genkit)  │
└─────────────────┘         └───────────────────┘         └────────────────┘
                                     │                            ▲
                                     ▼                            │
                            ┌───────────────────┐         ┌────────────────┐
                            │  blackjack-       │         │  AI Models     │
                            │  strategy-hint.ts │─────────►  & Prompts     │
                            └───────────────────┘         └────────────────┘
```

## Key Components

### 1. Genkit AI Integration

**Location:** `src/ai/ai-instance.ts`

This file configures and initializes the Genkit AI client, setting up the connection to Google AI services.

```typescript
// Simplified example
import { genkit } from 'genkit';
import { googleAI, gemini20Flash, gemini15Pro } from '@genkit-ai/googleai';

// Initialize the Google AI provider
const googleAIProvider = googleAI({
  apiKey: process.env.GOOGLE_GENAI_API_KEY
});

// Create and configure the Genkit AI instance
export const ai = genkit({
  plugins: [googleAIProvider],
  model: gemini20Flash, // Default model
});
```

### 2. Blackjack Strategy Hint Flow

**Location:** `src/ai/flows/blackjack-strategy-hint.ts`

This is the core AI integration that:
- Defines input/output schemas for the AI model
- Creates a prompt that guides the AI on how to analyze the game state
- Implements the server action that calls the AI model
- Processes and validates the AI's response
- Provides fallback responses when the AI service is unavailable

**Input Schema:**
```typescript
const BlackjackStrategyHintInputSchema = z.object({
  playerHand: z.array(z.number()),
  dealerUpCard: z.number(),
  playerHasAce: z.boolean(),
  canSplit: z.boolean().optional(),
  handTotal: z.number().optional(),
  gameVariant: z.string().optional(),
  runningCount: z.number().optional(),
  trueCount: z.number().optional(),
});
```

**Output Schema:**
```typescript
const BlackjackStrategyHintOutputSchema = z.object({
  recommendedAction: z.enum(['hit', 'stand', 'double', 'split', 'surrender']),
  advice: z.string(),
  reason: z.string().optional(),
  confidence: z.number().min(0).max(1).optional(),
  followsBasicStrategy: z.boolean().optional(),
  countingImplication: z.string().optional(),
});
```

### 3. Strategy Hint Hook

**Location:** `src/hooks/useStrategyHint.ts`

This React hook interfaces between the UI components and the AI server action, managing state, loading indicators, and error handling.

## Implementation Details

### Prompt Engineering

The AI prompt is carefully designed to:
1. Establish the AI as a Blackjack strategy expert
2. Provide the necessary game state information (player hand, dealer's upcard, etc.)
3. Include card counting information when available
4. Request a structured response with:
   - The recommended action (hit, stand, double, split, surrender)
   - A clear explanation in simple language
   - Technical reasoning for advanced players
   - Confidence level
   - Whether the recommendation follows basic strategy
   - How card counting affects the decision

### Error Handling

The AI integration includes robust error handling:
- Timeouts for when the AI service is slow to respond
- Fallback recommendations when the AI service is unavailable
- Validation of AI responses to ensure they match the expected schema
- Graceful degradation when parts of the AI response are missing

### Performance Optimization

Several optimizations are implemented:
- Caching similar requests to reduce API calls
- Debouncing rapid request sequences
- Loading states to provide feedback during AI processing
- Local storage of common strategy recommendations

## Usage Example

Here's an example of how the AI strategy hint is used in a component:

```typescript
import { useBlackjack } from '@/hooks/useBlackjack';
import { useStrategyHint } from '@/hooks/useStrategyHint';

function GameControls() {
  const { gameState } = useBlackjack();
  const { hint, isLoading, error, getHint } = useStrategyHint();

  // Request a hint when the user clicks the hint button
  const handleHintRequest = async () => {
    await getHint(gameState);
  };

  return (
    <div>
      <button onClick={handleHintRequest} disabled={isLoading}>
        {isLoading ? 'Getting hint...' : 'Get Strategy Hint'}
      </button>

      {hint && (
        <div className="hint-container">
          <h3>Recommended: {hint.recommendedAction}</h3>
          <p>{hint.advice}</p>
          {hint.reason && <p className="technical">{hint.reason}</p>}
          {hint.countingImplication && (
            <p className="counting-info">{hint.countingImplication}</p>
          )}
        </div>
      )}

      {error && <p className="error">{error}</p>}
    </div>
  );
}
```

## Future Enhancements

Planned enhancements to the AI integration include:
1. More detailed explanations of card counting implications
2. Personalized strategy recommendations based on player skill level
3. Training mode with step-by-step reasoning
4. Performance analysis to highlight patterns in decision-making errors
5. Multi-turn conversations with the AI for more in-depth strategy discussions