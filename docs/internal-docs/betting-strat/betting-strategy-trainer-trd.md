# Technical Requirements Document: Betting Strategy Trainer

## 1. Technical Overview

The Betting Strategy Trainer feature will enhance the Stack Advantage blackjack trainer by teaching users how to effectively vary their bet sizes based on the count. This document outlines the technical implementation details required to deliver this feature, focusing on architecture, data structures, components, and integration points.

## 2. Architecture and Implementation Approach

### 2.1 Component Architecture

The implementation will follow the existing architecture pattern:
- Extend the `BlackjackGame` class to handle betting strategy logic
- Update game settings and constants to include betting strategy configuration
- Create new UI components for betting strategy visualization and control
- Integrate with the existing `useBlackjack` hook for state management
- Add a simulation engine for risk analysis

### 2.2 Implementation Strategy

1. Extend data structures and constants
2. Modify the `BlackjackGame` class to implement betting strategy logic
3. Create UI components for betting strategy configuration and visualization
4. Implement the simulation engine
5. Integrate with existing game flow
6. Add educational tooltips and information
7. Implement automated and manual testing

## 3. Data Structure Modifications

### 3.1 Game Settings Extension

Update `GameSettings` interface in `src/types/game.ts`:

```typescript
export interface GameSettings {
  // Existing properties...
  
  /** Betting strategy configuration */
  bettingStrategy: BettingStrategy;
  
  /** Whether to show betting recommendations */
  showBettingRecommendations: boolean;
}

/** Betting strategy configuration */
export interface BettingStrategy {
  /** Type of betting strategy */
  type: 'fixed' | 'spread' | 'custom';
  
  /** Minimum bet amount */
  minBet: number;
  
  /** Maximum bet amount */
  maxBet: number;
  
  /** Betting units as count increases */
  spreadUnits: Record<number, number>;
  
  /** Whether to use true count or running count for bet sizing */
  useTrueCount: boolean;
  
  /** Risk tolerance (affects bet sizing) */
  riskTolerance: 'conservative' | 'moderate' | 'aggressive';
}

/** Betting performance metrics */
export interface BettingPerformance {
  /** Percentage of optimal bets made */
  bettingEfficiency: number;
  
  /** Theoretical win/loss with perfect betting */
  theoreticalWinLoss: number;
  
  /** Correlation between bet size and count */
  bettingCorrelation: number;
  
  /** History of betting decisions */
  bettingHistory: BettingDecision[];
}

/** Individual betting decision */
export interface BettingDecision {
  /** Hand ID */
  handId: string;
  
  /** True count at time of bet */
  trueCount: number;
  
  /** Actual bet placed */
  actualBet: number;
  
  /** Recommended bet */
  recommendedBet: number;
  
  /** Whether the bet was optimal */
  wasOptimal: boolean;
  
  /** Expected value of the bet */
  expectedValue: number;
  
  /** Outcome of the hand */
  outcome: 'win' | 'loss' | 'push' | 'blackjack';
  
  /** Amount won or lost */
  amountWonLost: number;
}
```

### 3.2 Constants Updates

Update `GAME_CONSTANTS` in `src/lib/constants.ts`:

```typescript
export const GAME_CONSTANTS = {
  // Existing constants...
  
  /** Default betting strategies */
  BETTING_STRATEGIES: {
    CONSERVATIVE: {
      type: 'spread' as const,
      minBet: 5,
      maxBet: 50,
      spreadUnits: {
        1: 1,  // 1x minimum bet at true count 1
        2: 2,  // 2x minimum bet at true count 2
        3: 4,  // 4x minimum bet at true count 3
        4: 6,  // 6x minimum bet at true count 4
        5: 8,  // 8x minimum bet at true count 5
        6: 10, // 10x minimum bet at true count 6+
      },
      useTrueCount: true,
      riskTolerance: 'conservative' as const,
    },
    MODERATE: {
      type: 'spread' as const,
      minBet: 5,
      maxBet: 100,
      spreadUnits: {
        1: 1,   // 1x minimum bet at true count 1
        2: 3,   // 3x minimum bet at true count 2
        3: 6,   // 6x minimum bet at true count 3
        4: 10,  // 10x minimum bet at true count 4
        5: 15,  // 15x minimum bet at true count 5
        6: 20,  // 20x minimum bet at true count 6+
      },
      useTrueCount: true,
      riskTolerance: 'moderate' as const,
    },
    AGGRESSIVE: {
      type: 'spread' as const,
      minBet: 5,
      maxBet: 200,
      spreadUnits: {
        1: 1,   // 1x minimum bet at true count 1
        2: 4,   // 4x minimum bet at true count 2
        3: 8,   // 8x minimum bet at true count 3
        4: 16,  // 16x minimum bet at true count 4
        5: 25,  // 25x minimum bet at true count 5
        6: 40,  // 40x minimum bet at true count 6+
      },
      useTrueCount: true,
      riskTolerance: 'aggressive' as const,
    },
  },
  
  /** Expected value per true count unit (approximate) */
  EV_PER_TRUE_COUNT: 0.005, // 0.5% per true count unit
} as const;
```

Update `DEFAULT_GAME_SETTINGS` in `src/lib/constants.ts`:

```typescript
export const DEFAULT_GAME_SETTINGS = {
  // Existing settings...
  
  bettingStrategy: GAME_CONSTANTS.BETTING_STRATEGIES.MODERATE,
  showBettingRecommendations: true,
} as const;
```

### 3.3 Game State Extension

Update `GameState` interface in `src/types/game.ts`:

```typescript
export interface GameState {
  // Existing properties...
  
  /** Recommended bet based on current count and strategy */
  recommendedBet: number;
  
  /** Expected value of current bet */
  expectedValue: number;
  
  /** Whether current bet is optimal */
  isOptimalBet: boolean;
  
  /** Betting performance metrics */
  bettingPerformance: BettingPerformance;
  
  /** Risk of ruin with current strategy and bankroll */
  riskOfRuin: number;
}
```

## 4. BlackjackGame Class Modifications

### 4.1 New Properties

Add the following properties to `BlackjackGame`:

```typescript
/** Betting performance tracking */
private bettingPerformance: BettingPerformance;

/** Current recommended bet */
private recommendedBet: number;

/** Expected value of current bet */
private expectedValue: number;

/** Whether current bet is optimal */
private isOptimalBet: boolean;

/** Risk of ruin calculation */
private riskOfRuin: number;
```

### 4.2 Constructor Updates

Update the constructor to initialize new properties:

```typescript
constructor(params?: GameInitParams) {
  // Existing initialization...
  
  this.bettingPerformance = {
    bettingEfficiency: 1,
    theoreticalWinLoss: 0,
    bettingCorrelation: 1,
    bettingHistory: [],
  };
  
  this.recommendedBet = this.settings.bettingStrategy.minBet;
  this.expectedValue = 0;
  this.isOptimalBet = true;
  this.riskOfRuin = 0;
  
  // Calculate initial recommended bet
  this.updateBettingRecommendation();
}
```

### 4.3 New Methods

Add the following methods to `BlackjackGame`:

```typescript
/**
 * Updates the betting recommendation based on current count and strategy
 * @private
 */
private updateBettingRecommendation(): void {
  const count = this.settings.bettingStrategy.useTrueCount ? this.trueCount : this.runningCount;
  
  // Default to minimum bet for zero or negative counts
  if (count <= 0) {
    this.recommendedBet = this.settings.bettingStrategy.minBet;
    this.expectedValue = 0;
    return;
  }
  
  // Calculate recommended bet based on strategy type
  if (this.settings.bettingStrategy.type === 'fixed') {
    this.recommendedBet = this.settings.bettingStrategy.minBet;
  } else {
    // Get the highest count threshold that's less than or equal to the current count
    const thresholds = Object.keys(this.settings.bettingStrategy.spreadUnits)
      .map(Number)
      .filter(threshold => threshold <= count)
      .sort((a, b) => b - a);
    
    const threshold = thresholds[0] || 0;
    const units = threshold > 0 ? this.settings.bettingStrategy.spreadUnits[threshold] : 1;
    
    this.recommended