# Technical Requirements Document: Drills Mode

## 1. Technical Overview

The Drills Mode feature will enhance the Stack Advantage blackjack trainer by providing focused practice exercises specifically designed to improve card counting speed and accuracy. This document outlines the technical implementation details required to deliver this feature, focusing on architecture, data structures, components, and integration points.

## 2. Architecture and Implementation Approach

### 2.1 Component Architecture

The implementation will follow the existing architecture pattern:
- Create a new `DrillsEngine` class to handle drill logic and state management
- Define new types and interfaces for drill configuration and results
- Create new UI components for drill selection, execution, and results display
- Implement a new `useDrills` hook for React state management
- Integrate with existing game components where appropriate

### 2.2 Implementation Strategy

1. Define data structures for drills configuration and results
2. Implement the core `DrillsEngine` class
3. Create the `useDrills` React hook
4. Develop UI components for the drills experience
5. Implement performance tracking and storage
6. Add educational content and tooltips
7. Integrate with the main application
8. Implement automated testing

## 3. Data Structure Modifications

### 3.1 Drill Types and Configuration

Create new types in `src/types/drills.ts`:

```typescript
/** Available drill types */
export type DrillType = 
  | 'runningCount' 
  | 'trueCount' 
  | 'speed' 
  | 'distraction' 
  | 'deckEstimation'
  | 'spotCheck';

/** Difficulty levels for drills */
export type DrillDifficulty = 'beginner' | 'intermediate' | 'advanced' | 'expert';

/** Speed settings for card display */
export type CardSpeed = 'slow' | 'medium' | 'fast' | 'expert';

/** Configuration for a drill session */
export interface DrillConfig {
  /** Type of drill */
  type: DrillType;
  
  /** Difficulty level */
  difficulty: DrillDifficulty;
  
  /** Duration in seconds (0 for non-timed drills) */
  duration: number;
  
  /** Speed of card display */
  cardSpeed: CardSpeed;
  
  /** Whether to show visual hints */
  showHints: boolean;
  
  /** Number of decks to use */
  deckCount: number;
  
  /** Custom options specific to each drill type */
  options: Record<string, any>;
}

/** Saved drill configuration with name */
export interface SavedDrillConfig extends DrillConfig {
  /** User-defined name for this configuration */
  name: string;
  
  /** Date when this configuration was saved */
  savedAt: string;
}
```

### 3.2 Drill Results and Performance

Add types for tracking drill results:

```typescript
/** Results from a single drill session */
export interface DrillResult {
  /** ID of this drill session */
  id: string;
  
  /** Configuration used for this session */
  config: DrillConfig;
  
  /** When the drill was started */
  startTime: string;
  
  /** When the drill was completed */
  endTime: string;
  
  /** Overall accuracy percentage */
  accuracy: number;
  
  /** Cards processed per minute */
  cardsPerMinute: number;
  
  /** Average response time in milliseconds */
  avgResponseTime: number;
  
  /** Detailed results for each interaction */
  details: DrillInteractionResult[];
  
  /** Achievement badges earned in this session */
  badgesEarned: string[];
  
  /** Points earned in this session */
  pointsEarned: number;
}

/** Result of a single interaction within a drill */
export interface DrillInteractionResult {
  /** Timestamp of this interaction */
  timestamp: number;
  
  /** Cards shown in this interaction */
  cards: Card[];
  
  /** Expected count after these cards */
  expectedCount: number;
  
  /** User's count input */
  userCount: number;
  
  /** Whether the user's input was correct */
  isCorrect: boolean;
  
  /** Response time in milliseconds */
  responseTime: number;
}

/** User's drill performance history */
export interface DrillPerformanceHistory {
  /** Results of all completed drill sessions */
  sessions: DrillResult[];
  
  /** Performance metrics by drill type */
  byDrillType: Record<DrillType, DrillTypePerformance>;
  
  /** Achievement badges earned */
  badges: string[];
  
  /** Total points earned across all drills */
  totalPoints: number;
  
  /** Current user level */
  level: number;
}

/** Performance metrics for a specific drill type */
export interface DrillTypePerformance {
  /** Number of sessions completed */
  sessionsCompleted: number;
  
  /** Best accuracy achieved */
  bestAccuracy: number;
  
  /** Best speed achieved (cards per minute) */
  bestSpeed: number;
  
  /** Average accuracy across all sessions */
  avgAccuracy: number;
  
  /** Average speed across all sessions */
  avgSpeed: number;
  
  /** Date of last session */
  lastPlayed: string;
}
```

### 3.3 Constants Updates

Add drill-related constants in `src/lib/constants.ts`:

```typescript
export const DRILL_CONSTANTS = {
  /** Card display speeds in milliseconds */
  CARD_SPEEDS: {
    SLOW: 2000,
    MEDIUM: 1000,
    FAST: 500,
    EXPERT: 250
  },
  
  /** Default durations in seconds */
  DEFAULT_DURATIONS: {
    BEGINNER: 60,
    INTERMEDIATE: 120,
    ADVANCED: 180,
    EXPERT: 300
  },
  
  /** Points awarded for different achievements */
  POINTS: {
    PERFECT_ACCURACY: 100,
    SPEED_THRESHOLD: 50,
    COMPLETION: 10,
    CORRECT_ANSWER: 1
  },
  
  /** Achievement badge definitions */
  BADGES: {
    PERFECT_NOVICE: {
      id: 'perfect_novice',
      name: 'Perfect Novice',
      description: 'Complete a beginner drill with 100% accuracy',
      icon: '🎯'
    },
    SPEED_DEMON: {
      id: 'speed_demon',
      name: 'Speed Demon',
      description: 'Process more than 100 cards per minute with at least 90% accuracy',
      icon: '⚡'
    },
    // Additional badges...
  },
  
  /** Default drill configurations */
  DEFAULT_CONFIGS: {
    RUNNING_COUNT_BEGINNER: {
      type: 'runningCount' as DrillType,
      difficulty: 'beginner' as DrillDifficulty,
      duration: 60,
      cardSpeed: 'slow' as CardSpeed,
      showHints: true,
      deckCount: 1,
      options: {
        pauseAfterCards: 5
      }
    },
    // Additional default configurations...
  }
} as const;
```

## 4. DrillsEngine Class

Create a new class in `src/lib/drills/drills-engine.ts`:

```typescript
/**
 * Core engine for managing drill sessions
 */
export class DrillsEngine {
  /** Current drill configuration */
  private config: DrillConfig;
  
  /** Cards to be used in this drill */
  private cards: Card[];
  
  /** Current position in the card sequence */
  private currentPosition: number;
  
  /** Current running count */
  private runningCount: number;
  
  /** Current true count */
  private trueCount: number;
  
  /** Remaining decks */
  private remainingDecks: number;
  
  /** Start time of the current drill */
  private startTime: number;
  
  /** Results of interactions in this drill */
  private interactionResults: DrillInteractionResult[];
  
  /** Whether the drill is currently active */
  private isActive: boolean;
  
  /** Timer ID for timed drills */
  private timerId: number | null;
  
  /**
   * Creates a new DrillsEngine instance
   * @param config Initial drill configuration
   */
  constructor(config: DrillConfig) {
    this