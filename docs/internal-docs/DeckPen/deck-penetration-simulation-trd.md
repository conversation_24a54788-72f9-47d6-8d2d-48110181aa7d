# Technical Requirements Document: Deck Penetration Simulation

## 1. Technical Overview

The Deck Penetration Simulation feature will enhance the Stack Advantage blackjack trainer by allowing users to adjust and visualize how deep into the shoe cards are dealt before reshuffling. This document outlines the technical implementation details required to deliver this feature.

## 2. Architecture and Implementation Approach

### 2.1 Component Architecture

The implementation will follow the existing architecture pattern:
- Extend the `BlackjackGame` class to handle penetration logic
- Update game settings and constants to include penetration configuration
- Create new UI components for visualization and control
- Integrate with the existing `useBlackjack` hook for state management

### 2.2 Implementation Strategy

1. Extend data structures and constants
2. Modify the `BlackjackGame` class to implement penetration logic
3. Create UI components for visualization and control
4. Integrate with existing game flow
5. Add educational tooltips and information
6. Implement automated and manual testing

## 3. Data Structure Modifications

### 3.1 Game Settings Extension

Update `GameSettings` interface in `src/types/game.ts`:

```typescript
export interface GameSettings {
  // Existing properties...
  
  /** Penetration percentage (how deep into the shoe cards are dealt before reshuffling) */
  penetration: number;
  
  /** Whether to show visual warning when approaching penetration threshold */
  showPenetrationWarning: boolean;
}
```

### 3.2 Constants Updates

Update `GAME_CONSTANTS` in `src/lib/constants.ts`:

```typescript
export const GAME_CONSTANTS = {
  // Existing constants...
  
  /** Default penetration (percentage of cards dealt before reshuffling) */
  DEFAULT_PENETRATION: 0.75,
  
  /** Penetration warning threshold (percentage of penetration) */
  PENETRATION_WARNING_THRESHOLD: 0.9,
  
  /** Preset penetration levels */
  PENETRATION_PRESETS: {
    SHALLOW: 0.6,
    MODERATE: 0.75,
    DEEP: 0.85
  }
} as const;
```

Update `DEFAULT_GAME_SETTINGS` in `src/lib/constants.ts`:

```typescript
export const DEFAULT_GAME_SETTINGS = {
  // Existing settings...
  
  penetration: GAME_CONSTANTS.DEFAULT_PENETRATION,
  showPenetrationWarning: true,
} as const;
```

### 3.3 Game State Extension

Update `GameState` interface to include penetration-related information:

```typescript
export interface GameState {
  // Existing properties...
  
  /** Total cards in the shoe */
  totalCards: number;
  
  /** Cards dealt from the shoe */
  cardsDealt: number;
  
  /** Current penetration threshold in cards */
  penetrationThreshold: number;
  
  /** Whether the penetration threshold has been reached */
  penetrationThresholdReached: boolean;
  
  /** Whether approaching penetration threshold (within warning range) */
  approachingPenetrationThreshold: boolean;
}
```

## 4. BlackjackGame Class Modifications

### 4.1 New Properties

Add to `BlackjackGame` class in `src/lib/game/blackjack.ts`:

```typescript
/** Total cards in the shoe */
private totalCards: number;

/** Cards dealt from the shoe */
private cardsDealt: number;
```

### 4.2 Constructor Updates

Update the constructor to initialize new properties:

```typescript
constructor(params: GameInitParams = {}) {
  // Existing code...
  
  this.totalCards = this.settings.deckCount * 52;
  this.cardsDealt = 0;
  
  // Update state with penetration information
  this.updatePenetrationState();
}
```

### 4.3 New Methods

Add the following methods to `BlackjackGame`:

```typescript
/**
 * Updates the penetration-related state
 * @private
 */
private updatePenetrationState(): void {
  const penetrationThreshold = Math.floor(this.totalCards * this.settings.penetration);
  const warningThreshold = penetrationThreshold * GAME_CONSTANTS.PENETRATION_WARNING_THRESHOLD;
  
  this.state.totalCards = this.totalCards;
  this.state.cardsDealt = this.cardsDealt;
  this.state.penetrationThreshold = penetrationThreshold;
  this.state.penetrationThresholdReached = this.cardsDealt >= penetrationThreshold;
  this.state.approachingPenetrationThreshold = 
    this.cardsDealt >= warningThreshold && this.cardsDealt < penetrationThreshold;
}

/**
 * Sets the penetration percentage
 * @param penetration Penetration percentage (0.5-0.9)
 * @public
 */
public setPenetration(penetration: number): void {
  // Validate input
  const validPenetration = Math.max(0.5, Math.min(0.9, penetration));
  
  // Update setting
  this.settings.penetration = validPenetration;
  
  // Update state
  this.updatePenetrationState();
}

/**
 * Gets the current penetration percentage
 * @returns Current penetration percentage
 * @public
 */
public getPenetration(): number {
  return this.settings.penetration;
}

/**
 * Gets the current penetration progress (0-1)
 * @returns Current penetration progress
 * @public
 */
public getPenetrationProgress(): number {
  return this.cardsDealt / this.totalCards;
}

/**
 * Gets the remaining cards until penetration threshold
 * @returns Number of cards until threshold is reached
 * @public
 */
public getRemainingCardsUntilThreshold(): number {
  const threshold = Math.floor(this.totalCards * this.settings.penetration);
  return Math.max(0, threshold - this.cardsDealt);
}
```

### 4.4 Method Modifications

Update the following existing methods:

```typescript
/**
 * Draws a card from the deck
 * @returns The drawn card or null if deck is empty
 * @private
 */
private drawCard(): Card | null {
  // Existing code...
  
  // Increment cards dealt counter
  this.cardsDealt++;
  
  // Update penetration state
  this.updatePenetrationState();
  
  return card;
}

/**
 * Check if the player needs to shuffle the deck
 * @returns Whether a shuffle is needed
 * @public
 */
public shouldShuffle(): boolean {
  // Check if penetration threshold has been reached
  return this.state.penetrationThresholdReached;
}

/**
 * Manually shuffle the deck
 * @returns Success status
 * @public
 */
public shuffleDeck(): boolean {
  // Existing code...
  
  // Reset cards dealt counter
  this.cardsDealt = 0;
  
  // Update penetration state
  this.updatePenetrationState();
  
  return true;
}
```

## 5. UI Components

### 5.1 PenetrationControl Component

Create `src/components/game/PenetrationControl.tsx`:

```typescript
/**
 * Component for adjusting deck penetration
 */
interface PenetrationControlProps {
  /** Current penetration value (0.5-0.9) */
  value: number;
  /** Callback when penetration changes */
  onChange: (value: number) => void;
  /** Whether to show preset buttons */
  showPresets?: boolean;
}

export function PenetrationControl({ 
  value, 
  onChange, 
  showPresets = true 
}: PenetrationControlProps) {
  // Implementation details...
}
```

### 5.2 ShoeVisualization Component

Create `src/components/game/ShoeVisualization.tsx`:

```typescript
/**
 * Component for visualizing shoe penetration
 */
interface ShoeVisualizationProps {
  /** Total cards in the shoe */
  totalCards: number;
  /** Cards dealt from the shoe */
  cardsDealt: number;
  /** Penetration threshold in cards */
  penetrationThreshold: number;
  /** Whether to show warning indicators */
  showWarnings?: boolean;
  /** Compact mode for mobile */
  compact?: boolean;
}

export function ShoeVisualization({
  totalCards,
  cardsDealt,
  penetrationThreshold,
  showWarnings = true,
  compact = false
}: ShoeVisualizationProps) {
  // Implementation details...
}
```

### 5.3 PenetrationSettings Component

Create `src/components/game/PenetrationSettings.tsx`:

```typescript
/**
 * Component for penetration settings in the settings panel
 */
interface PenetrationSettingsProps {
  /** Current penetration value */
  penetration: number;
  /** Callback when penetration changes */
  onPenetrationChange: (value: number) => void;
  /** Whether to show warnings */
  showWarnings: boolean;
  /** Callback when showWarnings changes */
  onShowWarningsChange: (value: boolean) => void;
}

export function PenetrationSettings({
  penetration,
  onPenetrationChange,
  showWarnings,
  onShowWarningsChange
}: PenetrationSettingsProps) {
  // Implementation details...
}
```

### 5.4 PenetrationTooltip Component

Create `src/components/game/PenetrationTooltip.tsx`:

```typescript
/**
 * Educational tooltip about penetration
 */
interface PenetrationTooltipProps {
  /** Current penetration value */
  penetration: number;
  /** Current true count (for advantage calculation) */
  trueCount?: number;
}

export function PenetrationTooltip({
  penetration,
  trueCount
}: PenetrationTooltipProps) {
  // Implementation details...
}
```

## 6. Hook Modifications

### 6.1 useBlackjack Hook Updates

Update `src/hooks/useBlackjack.ts`:

```typescript
/**
 * Add penetration-related state and methods to useBlackjack hook
 */
export function useBlackjack(initParams?: GameInitParams) {
  // Existing code...
  
  // Add penetration state
  const [penetration, setPenetration] = useState(
    initParams?.settings?.penetration || DEFAULT_GAME_SETTINGS.penetration
  );
  
  const [showPenetrationWarnings, setShowPenetrationWarnings] = useState(
    initParams?.settings?.showPenetrationWarning || DEFAULT_GAME_SETTINGS.showPenetrationWarning
  );
  
  // Add penetration methods
  const updatePenetration = useCallback((value: number) => {
    if (!game.current) return;
    
    game.current.setPenetration(value);
    setPenetration(value);
    
    // Update game state
    setGameState(game.current.getGameState());
  }, [game]);
  
  // Update useEffect for game settings
  useEffect(() => {
    if (!game.current) return;
    
    game.current.updateSettings({
      // Existing settings...
      penetration,
      showPenetrationWarning: showPenetrationWarnings
    });
    
    setGameState(game.current.getGameState());
  }, [/* existing dependencies */, penetration, showPenetrationWarnings]);
  
  // Return additional properties
  return {
    // Existing properties...
    
    penetration,
    updatePenetration,
    showPenetrationWarnings,
    setShowPenetrationWarnings,
    
    // Computed properties
    penetrationProgress: gameState.cardsDealt / gameState.totalCards,
    remainingCardsUntilThreshold: gameState.penetrationThreshold - gameState.cardsDealt,
    isPenetrationThresholdReached: gameState.penetrationThresholdReached,
    isApproachingPenetrationThreshold: gameState.approachingPenetrationThreshold
  };
}
```

## 7. Integration Points

### 7.1 Game Settings Panel

Update the game settings panel to include penetration settings:

```tsx
// In src/components/game/GameSettings.tsx or equivalent
<PenetrationSettings
  penetration={penetration}
  onPenetrationChange={updatePenetration}
  showWarnings={showPenetrationWarnings}
  onShowWarningsChange={setShowPenetrationWarnings}
/>
```

### 7.2 Game UI

Add shoe visualization to the game UI:

```tsx
// In src/app/page.tsx or equivalent
<ShoeVisualization
  totalCards={gameState.totalCards}
  cardsDealt={gameState.cardsDealt}
  penetrationThreshold={gameState.penetrationThreshold}
  showWarnings={showPenetrationWarnings && gameMode === GAME_MODES.PRACTICE}
  compact={isMobile}
/>
```

### 7.3 Shuffle Button

Update the shuffle button to show warning when threshold is reached:

```tsx
// In src/components/game/GameControls.tsx or equivalent
<Button
  onClick={shuffleDeck}
  variant={isPenetrationThresholdReached ? "warning" : "secondary"}
  disabled={gameState.status !== 'betting'}
>
  {isPenetrationThresholdReached ? "Shuffle Needed" : "Shuffle"}
</Button>
```

## 8. Testing Requirements

### 8.1 Unit Tests

- Test `BlackjackGame` penetration-related methods
- Test UI components with various inputs
- Test `useBlackjack` hook penetration functionality

### 8.2 Integration Tests

- Test penetration threshold triggering shuffle
- Test visualization updates as cards are dealt
- Test settings persistence between game sessions

### 8.3 UI/UX Tests

- Test responsive design on various screen sizes
- Test accessibility of all new components
- Test color contrast for warning indicators

### 8.4 Edge Cases

- Test with minimum (50%) and maximum (90%) penetration values
- Test with single deck and multiple decks
- Test manual shuffle before threshold is reached
- Test game behavior when threshold is reached mid-hand

## 9. Performance Considerations

- Ensure penetration calculations don't impact game performance
- Optimize shoe visualization rendering
- Consider lazy loading educational content

## 10. Accessibility Requirements

- Ensure all controls have proper ARIA labels