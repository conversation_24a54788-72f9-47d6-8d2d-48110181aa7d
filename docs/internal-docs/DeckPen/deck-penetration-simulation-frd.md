# Functional Requirements Document: Deck Penetration Simulation

## 1. Feature Overview

### Purpose
The Deck Penetration Simulation feature will allow users to adjust and visualize how deep into the shoe cards are dealt before reshuffling occurs. This simulates real casino conditions where different establishments have varying penetration policies, directly affecting card counting effectiveness.

### Business Value
- Enhances training realism by simulating actual casino conditions
- Teaches users how penetration affects counting effectiveness and betting strategy
- Prepares users for different casino environments they may encounter
- Complements the Training Modes feature by adding another dimension of realism

## 2. User Stories

### Primary User Stories
1. As a card counting student, I want to adjust deck penetration levels so I can practice under different casino conditions.
2. As an advanced player, I want to see how penetration affects my counting advantage so I can optimize my betting strategy.
3. As a trainer user, I want visual feedback on remaining cards vs. penetration threshold so I can anticipate when shuffles will occur.
4. As a challenge mode player, I want realistic penetration simulation so my training matches real-world conditions.

### Secondary User Stories
1. As a statistics-focused user, I want to see how different penetration levels affect my win rate so I can understand its importance.
2. As a beginner, I want educational tooltips about penetration so I can learn why it matters to card counters.
3. As a mobile user, I want an intuitive penetration control that works well on small screens.

## 3. Functional Requirements

### 3.1 Penetration Settings
- FR1.1: Add a penetration percentage setting (50-90%) to game settings
- FR1.2: Default penetration should be 75% (consistent with current constants)
- FR1.3: Penetration setting must persist between game sessions
- FR1.4: Provide preset options for common casino penetration levels (shallow: 50-60%, moderate: 65-75%, deep: 80-90%)

### 3.2 Shuffle Triggers
- FR2.1: Automatically trigger shuffle when the penetration threshold is reached
- FR2.2: Provide visual indication when approaching the penetration threshold (within 10% of threshold)
- FR2.3: In Challenge Mode, hide exact card counts but still indicate when approaching shuffle threshold
- FR2.4: Allow manual shuffle at any time regardless of penetration

### 3.3 Visualization
- FR3.1: Display a visual representation of the shoe showing total cards, dealt cards, and penetration threshold
- FR3.2: Update the visualization in real-time as cards are dealt
- FR3.3: Use color coding to indicate proximity to penetration threshold
- FR3.4: Ensure visualization is accessible and understandable on all device sizes

### 3.4 Educational Elements
- FR4.1: Provide tooltip explaining penetration's impact on counting effectiveness
- FR4.2: Include brief explanation of why casinos use different penetration levels
- FR4.3: Show estimated player advantage based on current count and penetration

## 4. UI/UX Specifications

### 4.1 Penetration Control
- A slider control for adjusting penetration percentage (50-90%)
- Preset buttons for quick selection of common penetration levels
- Clear numerical display of current penetration percentage
- Located in the game settings panel

### 4.2 Shoe Visualization
- Compact visual representation of the shoe that fits in the game UI
- Progress bar or similar element showing:
  - Total shoe size (based on number of decks)
  - Current position in the shoe
  - Penetration threshold marker
- Color coding:
  - Green: Far from penetration threshold
  - Yellow: Approaching threshold (within 10%)
  - Red: At or past threshold (shuffle needed)

### 4.3 Shuffle Notification
- Visual indicator when shuffle is needed
- Optional sound effect for shuffle notification
- Automatic shuffle animation when threshold is reached
- Manual shuffle button always available

### 4.4 Mobile Considerations
- Compact visualization that works on small screens
- Touch-friendly slider and controls
- Collapsible educational elements to save screen space

## 5. Acceptance Criteria

### 5.1 Penetration Settings
- AC1.1: User can adjust penetration from 50% to 90% in 5% increments
- AC1.2: Setting persists between game sessions
- AC1.3: Preset buttons correctly set penetration to predefined values
- AC1.4: Default value matches GAME_CONSTANTS.DEFAULT_PENETRATION

### 5.2 Shuffle Functionality
- AC2.1: Game automatically triggers shuffle when cards dealt exceeds penetration threshold
- AC2.2: Visual indicators change appropriately as approaching threshold
- AC2.3: Manual shuffle works regardless of current penetration
- AC2.4: After shuffle, all indicators reset correctly

### 5.3 Visualization
- AC3.1: Shoe visualization accurately reflects current state of the shoe
- AC3.2: Visualization updates in real-time with each card dealt
- AC3.3: Color coding changes at appropriate thresholds
- AC3.4: Visualization is visible and understandable on mobile devices

### 5.4 Educational Value
- AC4.1: Tooltips provide accurate information about penetration
- AC4.2: Estimated advantage calculation is mathematically sound
- AC4.3: Information is presented in an accessible, easy-to-understand format

### 5.5 Integration
- AC5.1: Feature works correctly in both Practice and Challenge modes
- AC5.2: Feature respects existing game settings and rules
- AC5.3: Performance is not negatively impacted by the new feature