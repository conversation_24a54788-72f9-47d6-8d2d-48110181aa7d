# React Hooks Documentation

This document provides information about the custom React hooks used in the Stack Advantage Blackjack Trainer to manage game state and user interactions.

## Core Hooks

### useBlackjack

**Location:** `src/hooks/useBlackjack.ts`

**Purpose:** The primary hook for managing the Blackjack game state and actions. It creates and maintains a `BlackjackGame` instance and provides methods to interact with it.

**Usage:**
```typescript
const {
  gameState,
  betAmount,
  betError,
  updateBetAmount,
  placeBet,
  hit,
  stand,
  doubleDown,
  split,
  startNewRound,
  getRecommendedBet,
  shouldShuffle,
  shuffleDeck
} = useBlackjack(initParams);
```

**Parameters:**
- `initParams?: GameInitParams` - Optional parameters for configuring the game

**Returns:**

| Property/Method | Type | Description |
|-----------------|------|-------------|
| `gameState` | `GameState` | Current state of the game |
| `betAmount` | `number` | Current bet amount |
| `betError` | `string` | Error message related to betting |
| `updateBetAmount` | `(amount: number) => void` | Updates the bet amount with validation |
| `placeBet` | `() => ActionResult` | Places a bet and deals initial cards |
| `hit` | `() => ActionResult` | Performs a hit action |
| `stand` | `() => ActionResult` | Performs a stand action |
| `doubleDown` | `() => ActionResult` | Performs a double down action |
| `split` | `() => ActionResult` | Performs a split action |
| `startNewRound` | `() => GameState` | Resets the game for a new round |
| `getRecommendedBet` | `() => number` | Gets the recommended bet based on true count |
| `shouldShuffle` | `() => boolean` | Checks if the deck should be shuffled |
| `shuffleDeck` | `() => boolean` | Creates a new shuffled deck |

**Implementation details:**
- Creates a singleton instance of `BlackjackGame`
- Maintains UI state for betting
- Provides methods that call the corresponding methods on the game instance
- Updates local React state when game state changes

### useStrategyHint

**Location:** `src/hooks/useStrategyHint.ts`

**Purpose:** Provides AI-powered strategy hints for the current game state by interfacing with the Genkit AI service.

**Usage:**
```typescript
const {
  hint,
  isLoading,
  error,
  getHint
} = useStrategyHint();
```

**Returns:**

| Property/Method | Type | Description |
|-----------------|------|-------------|
| `hint` | `GameHint \| null` | Current strategy hint, if available |
| `isLoading` | `boolean` | Whether a hint is currently being loaded |
| `error` | `string \| null` | Error message if hint request failed |
| `getHint` | `(gameState: GameState) => Promise<void>` | Requests a hint for the given game state |

**Implementation details:**
- Uses the `blackjackStrategyHint` server action from `src/ai/flows/blackjack-strategy-hint.ts`
- Extracts relevant information from the game state for the AI model
- Handles loading states and error scenarios

### useGameSettings

**Location:** `src/hooks/useGameSettings.ts`

**Purpose:** Manages game configuration settings and preferences.

**Usage:**
```typescript
const {
  settings,
  updateSettings,
  resetSettings
} = useGameSettings();
```

**Returns:**

| Property/Method | Type | Description |
|-----------------|------|-------------|
| `settings` | `GameSettings` | Current game settings |
| `updateSettings` | `(newSettings: Partial<GameSettings>) => void` | Updates specific settings |
| `resetSettings` | `() => void` | Resets settings to default values |

**Implementation details:**
- Uses `localStorage` to persist settings between sessions
- Provides validation for setting changes
- Initializes with default settings if none exist in storage

### usePerformanceMetrics

**Location:** `src/hooks/usePerformanceMetrics.ts`

**Purpose:** Tracks and analyzes player performance and strategy accuracy.

**Usage:**
```typescript
const {
  metrics,
  recordDecision,
  recordGame,
  resetMetrics
} = usePerformanceMetrics();
```

**Returns:**

| Property/Method | Type | Description |
|-----------------|------|-------------|
| `metrics` | `TrainingMetrics` | Current performance metrics |
| `recordDecision` | `(made: PlayerAction, optimal: PlayerAction) => void` | Records a player decision for tracking |
| `recordGame` | `(gameState: GameState, followedStrategy: boolean) => void` | Records game outcome for tracking |
| `resetMetrics` | `() => void` | Resets all metrics to zero |

**Implementation details:**
- Maintains counters for correct/incorrect decisions
- Calculates accuracy percentages
- Tracks profit/loss over time
- Persists data to `localStorage` for long-term tracking

## Utility Hooks

### useMobile

**Location:** `src/hooks/use-mobile.tsx`

**Purpose:** Detects if the current viewport is mobile-sized for responsive design.

**Usage:**
```typescript
const isMobile = useMobile();
```

**Returns:**
- `boolean` - `true` if the viewport width is below the mobile breakpoint

**Implementation details:**
- Uses media queries and the `useMediaQuery` hook
- Updates responsively when window size changes

## Hook Integration

The hooks are designed to work together in a coordinated way:

1. `useBlackjack` is the primary hook that manages the game state
2. `useGameSettings` configures the game parameters
3. `useStrategyHint` provides AI assistance for optimal play
4. `usePerformanceMetrics` tracks the player's performance

Components can combine these hooks to create a complete game experience with appropriate UI and behavior for different device sizes.