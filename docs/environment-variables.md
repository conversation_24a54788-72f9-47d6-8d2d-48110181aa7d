# Environment Variables

This document describes the environment variables used in the Stack Advantage Blackjack Trainer application.

## Setup

1. Copy the `.env.example` file to create a new `.env` file:
   ```bash
   cp .env.example .env
   ```

2. Edit the `.env` file to add your API keys and configure environment variables.

## Available Environment Variables

### AI Configuration

| Variable | Description | Default Value | Required |
|----------|-------------|---------------|----------|
| `GOOGLE_GENAI_API_KEY` | Google Gemini API key for AI features | - | Yes |
| `NEXT_PUBLIC_AI_MODEL` | The AI model to use (googleai/gemini-2.0-flash or googleai/gemini-1.5-pro) | googleai/gemini-2.0-flash | No |

#### Getting a Google Gemini API Key

1. Go to [Google AI Studio](https://makersuite.google.com/app/apikey)
2. Sign in with your Google account
3. Click on "Create API Key"
4. Copy the API key and paste it in your `.env` file

**Note:** If the AI features are not working, you might see a fallback message like "Unable to get AI recommendation at this time. Standing is generally the safest option." This indicates that the API key is invalid or the AI service is unavailable.

### AI Logging and Caching

| Variable | Description | Default Value | Required |
|----------|-------------|---------------|----------|
| `AI_LOGGING` | Enable AI logging | false | No |
| `AI_LOG_LEVEL` | Log level for AI operations | info | No |
| `AI_CACHING` | Enable caching of AI responses | true | No |

### Node Environment

| Variable | Description | Default Value | Required |
|----------|-------------|---------------|----------|
| `NODE_ENV` | Node environment (development, production, test) | development | No |

## Environment Variables in Next.js

Next.js has built-in support for environment variables, which allows you to:

- Use `.env` files for environment variables
- Access environment variables in the browser with the `NEXT_PUBLIC_` prefix
- Override environment variables with `.env.local`, `.env.development`, `.env.production`, etc.

For more information, see the [Next.js documentation on environment variables](https://nextjs.org/docs/basic-features/environment-variables).
