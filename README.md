# Stack Advantage - Blackjack Card Counting Trainer

## Overview

Stack Advantage is a web-based application designed to teach and reinforce the Hi-Lo card counting technique using the rules of Blackjack. It serves as a trainer for both beginners learning card counting and experienced players looking to refine their skills.

This project is built using the Next.js framework (App Router) with TypeScript, React for the UI, and Tailwind CSS with shadcn/ui components for styling.

## Purpose

The primary goal is to provide an interactive platform where users can:

*   Play Blackjack against a simulated dealer.
*   Learn and practice the Hi-Lo card counting system.
*   Receive feedback on their counting accuracy and strategic decisions.
*   Track their performance and progress over time.

(See `docs/blueprint.md` for detailed features and the original project vision.)

## Tech Stack

*   **Framework:** Next.js (App Router)
*   **Language:** TypeScript
*   **UI Library:** React
*   **Styling:** Tailwind CSS, shadcn/ui
*   **AI Integration:** Google AI (via Genkit) for strategy hints
*   **State Management:** React Hooks
*   **Linting/Formatting:** ESLint, Prettier (configured in `package.json`)

## Project Structure

*   `src/app/`: Contains the main application pages (using Next.js App Router).
*   `src/components/`: Reusable React components (including shadcn/ui components and game-specific components).
*   `src/hooks/`: Custom React hooks for managing game state and logic.
*   `src/lib/`: Utility functions and core game logic (like card handling and rules).
*   `src/ai/`: AI-related code, including Genkit flows for strategy hints.
*   `src/types/`: TypeScript type definitions.
*   `docs/`: Project documentation, including the blueprint and detailed guides.
*   `public/`: Static assets like card images.

## Key Features

1. **Blackjack Gameplay** - Full implementation of Blackjack rules with:
   - Multiple decks of cards
   - Hit, Stand, Double Down, and Split actions
   - Dealer stands on soft 17 (configurable)

2. **Card Counting Training** - Built-in support for:
   - Hi-Lo system (cards 2-6 = +1, 7-9 = 0, 10-A = -1)
   - Running count and true count tracking
   - Configurable deck penetration

3. **Strategy Assistance** - AI-powered hints that:
   - Recommend optimal basic strategy moves
   - Adapt to current count when appropriate
   - Provide explanations for learning

4. **Performance Metrics** - Track:
   - Win/loss ratio
   - Counting accuracy
   - Deviation from optimal strategy
   - Bankroll changes over time

## Getting Started

1.  **Prerequisites:**
    *   Node.js (version specified in `package.json` or latest LTS)
    *   npm or yarn

2.  **Installation:**
    ```bash
    npm install
    # or
    yarn install
    ```

3.  **Running the Development Server:**
    ```bash
    npm run dev
    # or
    yarn dev
    ```
    This will start the Next.js development server, typically on `http://localhost:9002`.

4.  **Environment Setup:**
    Copy the `.env.example` file to create a new `.env` file:
    ```bash
    cp .env.example .env
    ```
    Then edit the `.env` file to add your API keys and configure environment variables.

    Required environment variables:
    - `GOOGLE_GENAI_API_KEY`: Your Google Gemini API key for AI features (get one from [Google AI Studio](https://makersuite.google.com/app/apikey))
    - `NEXT_PUBLIC_AI_MODEL`: The AI model to use (options: googleai/gemini-2.0-flash or googleai/gemini-1.5-pro)

    **Note:** The AI features will not work without a valid Google Gemini API key. See `docs/environment-variables.md` for more details.

5.  **Running the Genkit AI Flow (for AI Hints):**
    In a separate terminal:
    ```bash
    npm run genkit:dev
    # or
    yarn genkit:dev
    ```
    This starts the Genkit development server required for the AI strategy hints feature.

## Game Rules

- **Decks**: Two standard decks by default (configurable)
- **Dealer**: Stands on all 17s including soft 17 (configurable)
- **Player Actions**: Hit, Stand, Double Down, Split
- **Doubling Down**: Allowed on any two cards, including after splits
- **Splitting**: Allowed for pairs. Double down after split is supported
- **Blackjack Payout**: 3:2 (configurable)

## Game Modes

- **Free Practice Mode**: No stakes, count displayed, focused on learning
- **Challenge Mode**: Hidden count, test decision-making under real pressure

## Contributing

Contributions are welcome! Please feel free to submit a Pull Request.

1. Fork the repository
2. Create your feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add some amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

See `docs/blueprint.md` for the project vision and roadmap.

## Key Dependencies

*   `next`: The React framework.
*   `react`, `react-dom`: UI library.
*   `tailwindcss`, `shadcn-ui`: Styling and UI components.
*   `@genkit-ai/googleai`, `@genkit-ai/next`, `genkit`: Google AI integration.
*   `typescript`, `eslint`, `prettier`: Development tools for type safety, linting, and formatting.

(See `package.json` for a full list of dependencies.)

## License

[MIT](https://choosealicense.com/licenses/mit/)
