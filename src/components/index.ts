// Create a barrel file to centralize component exports

// Export UI components (from shadcn/ui)
export * from './ui/accordion'
export * from './ui/alert-dialog'
export * from './ui/alert'
export * from './ui/avatar'
export * from './ui/badge'
export * from './ui/button'
export * from './ui/calendar'
export * from './ui/card'
export * from './ui/chart'
export * from './ui/checkbox'
export * from './ui/dialog'
export * from './ui/dropdown-menu'
export * from './ui/form'
export * from './ui/input'
export * from './ui/label'
export * from './ui/menubar'
export * from './ui/popover'
export * from './ui/progress'
export * from './ui/radio-group'
export * from './ui/scroll-area'
export * from './ui/select'
export * from './ui/separator'
export * from './ui/sheet'
export * from './ui/sidebar'
export * from './ui/skeleton'
export * from './ui/slider'
export * from './ui/switch'
export * from './ui/table'
export * from './ui/tabs'
export * from './ui/textarea'
export * from './ui/toast'
export * from './ui/toaster'
export * from './ui/tooltip'

// Export custom game-specific components
export { PlayingCard } from './game/Card' // Use named export and remove extension
export { ModeSelector } from './game/ModeSelector'
export { CountDisplay } from './game/CountDisplay'
export { CountVerification } from './game/CountVerification'
// Add exports for GameBoard, Controls, etc. when they are created in src/components/game/
// export * from './game/GameBoard'
// export * from './game/Controls'

// Export other shared components like icons
export * from './icons'