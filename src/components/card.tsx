/**
 * @fileOverview React component for rendering a playing card.
 */
'use client';

/**
 * Props for the Card component.
 */
interface CardProps {
  /** The card identifier string (e.g., 'HA' for Ace of Hearts, 'S K' for King of Spades). */
  card: string;
  /** Whether the card should be displayed face up or face down. */
  faceUp: boolean;
}

/**
 * Renders a single playing card.
 * Displays the suit and value if face up, or a card back if face down.
 * Uses Tailwind CSS for styling.
 * @param props - The component props.
 * @returns A React element representing the card.
 */
export const Card: React.FC<CardProps> = ({ card, faceUp }) => {
  const suit = card.charAt(0);
  const value = card.substring(1);

  /**
   * Determines the CSS color class for a given suit.
   * Hearts and Diamonds are red, Clubs and Spades are black.
   * @param suit - The suit character ('H', 'D', 'C', 'S').
   * @returns Tailwind CSS class string for the text color.
   */
  const getSuitColor = (suit: string) => {
    if (suit === 'H' || suit === 'D') {
      return 'text-red-500';
    } else {
      // Use a lighter black/gray for dark mode visibility if needed, but black is often fine on white bg
      return 'text-black'; // Keep text black for white card face
    }
  };

  /**
   * Gets the standard symbol for a given suit.
   * @param suit - The suit character ('H', 'D', 'C', 'S').
   * @returns The suit symbol (e.g., '♥', '♦', '♣', '♠').
   */
  const getSuitSymbol = (suit: string) => {
    switch (suit) {
      case 'H': return '♥';
      case 'D': return '♦';
      case 'C': return '♣';
      case 'S': return '♠';
      default: return suit;
    }
  };

  // Simplified card style, closer to the reference image (less clutter)
  return (
    <div className={`relative w-20 h-28 rounded-lg border border-gray-300 dark:border-gray-600 shadow-md text-2xl font-bold flex items-center justify-center ${faceUp ? 'bg-white' : 'bg-red-600'}`}> {/* Adjusted size, rounded-lg, shadow, white bg for face, red bg for back */}
      {faceUp ? (
        <>
          {/* Central Suit/Value Display */}
          <div className="text-center">
             <div className={`text-4xl ${getSuitColor(suit)}`}> {/* Larger central symbol */}
               {getSuitSymbol(suit)}
             </div>
             <div className={`text-xl font-semibold ${getSuitColor(suit)}`}> {/* Value below symbol */}
               {value === '10' ? '10' : value}
             </div>
          </div>
          {/* Optional: Keep small corner indicators if desired, but removed for cleaner look */}
        </>
      ) : (
        // Use a simple color or keep the SVG for the card back
        <div className="w-full h-full rounded-lg flex items-center justify-center">
           {/* Optional: Add a simple pattern or logo to the back */}
           {/* <span className="text-white text-4xl">?</span> */}
           {/* Alternative: Use an SVG for the card back */}
           {/* <img src="/card-back.svg" alt="Back of playing card" className="w-full h-full rounded-lg object-cover" /> */}
        </div>
      )}
    </div>
  );
};
