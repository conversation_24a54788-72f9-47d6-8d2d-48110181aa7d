/**
 * @fileOverview Count verification dialog for challenging players in challenge mode
 * Allows players to input their count and see accuracy results
 */

"use client"

import * as React from "react"
import { useState } from "react"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Badge } from "@/components/ui/badge"
import { Card, CardContent } from "@/components/ui/card"
import { Separator } from "@/components/ui/separator"
import { CheckCircle, XCircle, Target, TrendingUp, TrendingDown } from "lucide-react"
import type { CountVerificationResult } from "@/types/game"

interface CountVerificationProps {
  /** Whether the dialog is open */
  isOpen: boolean
  /** Callback when dialog should close */
  onClose: () => void
  /** Callback when count is verified */
  onVerify: (playerCount: number) => CountVerificationResult
  /** Verification result (null when in input state) */
  result?: CountVerificationResult | null
  /** Optional className for styling */
  className?: string
}

/**
 * Dialog component for count verification in challenge mode
 * Handles both input state and result display state
 */
export function CountVerification({ 
  isOpen, 
  onClose, 
  onVerify, 
  result,
  className 
}: CountVerificationProps) {
  const [playerCount, setPlayerCount] = useState<string>("")
  const [isLoading, setIsLoading] = useState(false)

  // Reset state when dialog opens
  React.useEffect(() => {
    if (isOpen && !result) {
      setPlayerCount("")
      setIsLoading(false)
    }
  }, [isOpen, result])

  const handleVerify = async () => {
    const count = parseInt(playerCount, 10)
    if (isNaN(count)) return

    setIsLoading(true)
    
    // Simulate a brief loading state for better UX
    setTimeout(() => {
      onVerify(count)
      setIsLoading(false)
    }, 300)
  }

  const handleContinue = () => {
    onClose()
  }

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !result && playerCount.trim() !== '') {
      handleVerify()
    }
  }

  // Helper function to get accuracy badge variant
  const getAccuracyBadgeVariant = (accuracy: number) => {
    if (accuracy >= 90) return "default" // Green
    if (accuracy >= 70) return "secondary" // Yellow-ish
    return "destructive" // Red
  }

  // Helper function to get feedback message
  const getFeedbackMessage = (result: CountVerificationResult) => {
    if (result.isCorrect) {
      return "Perfect! Your count is exactly right."
    }
    
    const difference = Math.abs(result.playerCount - result.actualCount)
    if (difference === 1) {
      return "Very close! You were off by just 1 point."
    } else if (difference <= 3) {
      return `Good effort! You were off by ${difference} points.`
    } else {
      return `Keep practicing! There's room for improvement.`
    }
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className={`sm:max-w-md ${className}`}>
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Target className="h-5 w-5" />
            Count Verification
          </DialogTitle>
          <DialogDescription>
            {!result 
              ? "What's your current running count? Enter your best estimate."
              : "Here's how you did compared to the actual count."
            }
          </DialogDescription>
        </DialogHeader>

        {!result ? (
          // Input State
          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="count-input">Your Running Count</Label>
              <Input
                id="count-input"
                type="number"
                placeholder="Enter count (e.g., +5, -3, 0)"
                value={playerCount}
                onChange={(e) => setPlayerCount(e.target.value)}
                onKeyPress={handleKeyPress}
                className="text-center text-lg font-mono"
                autoFocus
              />
              <p className="text-xs text-muted-foreground text-center">
                Include the sign for positive numbers (e.g., +5)
              </p>
            </div>
          </div>
        ) : (
          // Result State
          <div className="space-y-4">
            <Card>
              <CardContent className="pt-6">
                <div className="space-y-4">
                  {/* Accuracy Score */}
                  <div className="text-center">
                    <div className="flex items-center justify-center gap-2 mb-2">
                      {result.isCorrect ? (
                        <CheckCircle className="h-6 w-6 text-green-600" />
                      ) : (
                        <XCircle className="h-6 w-6 text-red-600" />
                      )}
                      <Badge 
                        variant={getAccuracyBadgeVariant(result.accuracy)}
                        className="text-lg px-3 py-1"
                      >
                        {result.accuracy.toFixed(0)}% Accurate
                      </Badge>
                    </div>
                    <p className="text-sm text-muted-foreground">
                      {getFeedbackMessage(result)}
                    </p>
                  </div>

                  <Separator />

                  {/* Count Comparison */}
                  <div className="grid grid-cols-2 gap-4">
                    <div className="text-center">
                      <p className="text-sm font-medium text-muted-foreground mb-1">
                        Your Count
                      </p>
                      <div className="text-2xl font-bold">
                        {result.playerCount > 0 ? '+' : ''}{result.playerCount}
                      </div>
                    </div>
                    <div className="text-center">
                      <p className="text-sm font-medium text-muted-foreground mb-1">
                        Actual Count
                      </p>
                      <div className="text-2xl font-bold text-green-600">
                        {result.actualCount > 0 ? '+' : ''}{result.actualCount}
                      </div>
                    </div>
                  </div>

                  {/* Difference */}
                  {!result.isCorrect && (
                    <div className="text-center">
                      <p className="text-sm text-muted-foreground">
                        Difference: {Math.abs(result.playerCount - result.actualCount)} points
                      </p>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          </div>
        )}

        <DialogFooter>
          {!result ? (
            <div className="flex gap-2 w-full">
              <Button 
                variant="outline" 
                onClick={onClose}
                className="flex-1"
              >
                Skip
              </Button>
              <Button 
                onClick={handleVerify}
                disabled={playerCount.trim() === '' || isLoading}
                className="flex-1"
              >
                {isLoading ? "Checking..." : "Verify Count"}
              </Button>
            </div>
          ) : (
            <Button onClick={handleContinue} className="w-full">
              Continue Playing
            </Button>
          )}
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
