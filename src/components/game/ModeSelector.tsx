/**
 * @fileOverview Mode selector component for switching between practice and challenge modes
 * Uses shadcn/ui Tabs component for a clean, accessible interface
 */

"use client"

import * as React from "react"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>bsTrigger, <PERSON>bsContent } from "@/components/ui/tabs"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Eye, EyeOff, BookOpen, Target } from "lucide-react"
import type { GameMode } from "@/types/game"

interface ModeSelectorProps {
  /** Current selected mode */
  currentMode: GameMode
  /** Callback when mode changes */
  onModeChange: (mode: GameMode) => void
  /** Optional className for styling */
  className?: string
}

/**
 * Component for selecting between Practice and Challenge modes
 * Provides clear descriptions of each mode's features and benefits
 */
export function ModeSelector({ currentMode, onModeChange, className }: ModeSelectorProps) {
  return (
    <div className={className}>
      <Tabs value={currentMode} onValueChange={(value) => onModeChange(value as GameMode)}>
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="practice" className="flex items-center gap-2">
            <BookOpen className="h-4 w-4" />
            Practice Mode
          </TabsTrigger>
          <TabsTrigger value="challenge" className="flex items-center gap-2">
            <Target className="h-4 w-4" />
            Challenge Mode
          </TabsTrigger>
        </TabsList>
        
        <TabsContent value="practice" className="mt-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <BookOpen className="h-5 w-5 text-blue-600" />
                Practice Mode
                <Badge variant="secondary" className="ml-2">
                  <Eye className="h-3 w-3 mr-1" />
                  Counts Visible
                </Badge>
              </CardTitle>
              <CardDescription>
                Perfect for learning and improving your card counting skills
              </CardDescription>
            </CardHeader>
            <CardContent>
              <ul className="space-y-2 text-sm text-muted-foreground">
                <li className="flex items-center gap-2">
                  <div className="h-1.5 w-1.5 bg-green-500 rounded-full" />
                  Running count and true count displayed in real-time
                </li>
                <li className="flex items-center gap-2">
                  <div className="h-1.5 w-1.5 bg-green-500 rounded-full" />
                  Strategy hints available to guide your decisions
                </li>
                <li className="flex items-center gap-2">
                  <div className="h-1.5 w-1.5 bg-green-500 rounded-full" />
                  No pressure environment for focused learning
                </li>
                <li className="flex items-center gap-2">
                  <div className="h-1.5 w-1.5 bg-green-500 rounded-full" />
                  Detailed feedback on each card's count value
                </li>
              </ul>
            </CardContent>
          </Card>
        </TabsContent>
        
        <TabsContent value="challenge" className="mt-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Target className="h-5 w-5 text-orange-600" />
                Challenge Mode
                <Badge variant="secondary" className="ml-2">
                  <EyeOff className="h-3 w-3 mr-1" />
                  Counts Hidden
                </Badge>
              </CardTitle>
              <CardDescription>
                Test your skills in a realistic casino-like environment
              </CardDescription>
            </CardHeader>
            <CardContent>
              <ul className="space-y-2 text-sm text-muted-foreground">
                <li className="flex items-center gap-2">
                  <div className="h-1.5 w-1.5 bg-orange-500 rounded-full" />
                  Count information hidden to simulate real play
                </li>
                <li className="flex items-center gap-2">
                  <div className="h-1.5 w-1.5 bg-orange-500 rounded-full" />
                  Limited or no strategy hints available
                </li>
                <li className="flex items-center gap-2">
                  <div className="h-1.5 w-1.5 bg-orange-500 rounded-full" />
                  Periodic count verification to test accuracy
                </li>
                <li className="flex items-center gap-2">
                  <div className="h-1.5 w-1.5 bg-orange-500 rounded-full" />
                  Pressure testing for real-world preparation
                </li>
              </ul>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
