/**
 * @fileOverview Count display component showing running count, true count, and remaining decks
 * Only visible in practice mode or when explicitly enabled
 */

"use client"

import * as React from "react"
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { TrendingUp, TrendingDown, Minus, Layers } from "lucide-react"
import type { Card as GameCard } from "@/types/game"

interface CountDisplayProps {
  /** Whether the count information should be visible */
  isVisible: boolean
  /** Current running count */
  runningCount: number
  /** Current true count */
  trueCount: number
  /** Last card dealt (to show its count value) */
  lastCard?: GameCard
  /** Number of decks remaining in the shoe */
  remainingDecks: number
  /** Function to get count value for a specific card */
  getCardCountValue?: (card: GameCard) => number
  /** Optional className for styling */
  className?: string
}

/**
 * Displays count information including running count, true count, and deck status
 * Provides visual indicators for positive/negative counts and last card value
 */
export function CountDisplay({ 
  isVisible, 
  runningCount, 
  trueCount, 
  lastCard, 
  remainingDecks,
  getCardCountValue,
  className 
}: CountDisplayProps) {
  // Don't render anything if not visible
  if (!isVisible) {
    return null
  }

  // Get the count value of the last card if available
  const lastCardValue = lastCard && getCardCountValue ? getCardCountValue(lastCard) : null

  // Helper function to get count color class
  const getCountColor = (count: number) => {
    if (count > 0) return "text-green-600 dark:text-green-400"
    if (count < 0) return "text-red-600 dark:text-red-400"
    return "text-muted-foreground"
  }

  // Helper function to get count icon
  const getCountIcon = (count: number) => {
    if (count > 0) return <TrendingUp className="h-4 w-4" />
    if (count < 0) return <TrendingDown className="h-4 w-4" />
    return <Minus className="h-4 w-4" />
  }

  // Helper function to get last card badge variant
  const getLastCardBadgeVariant = (value: number) => {
    if (value > 0) return "default" // Green-ish
    if (value < 0) return "destructive" // Red
    return "secondary" // Neutral
  }

  return (
    <Card className={className}>
      <CardHeader className="pb-3">
        <CardTitle className="text-lg flex items-center gap-2">
          Card Count Information
          <Badge variant="outline" className="text-xs">
            Practice Mode
          </Badge>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Running Count */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            {getCountIcon(runningCount)}
            <span className="font-medium">Running Count:</span>
          </div>
          <div className={`text-2xl font-bold ${getCountColor(runningCount)}`}>
            {runningCount > 0 ? '+' : ''}{runningCount}
          </div>
        </div>

        <Separator />

        {/* True Count */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            {getCountIcon(trueCount)}
            <span className="font-medium">True Count:</span>
          </div>
          <div className={`text-2xl font-bold ${getCountColor(trueCount)}`}>
            {trueCount > 0 ? '+' : ''}{trueCount.toFixed(1)}
          </div>
        </div>

        <Separator />

        {/* Remaining Decks */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Layers className="h-4 w-4" />
            <span className="font-medium">Decks Remaining:</span>
          </div>
          <div className="text-lg font-semibold text-muted-foreground">
            {remainingDecks.toFixed(1)}
          </div>
        </div>

        {/* Last Card Value */}
        {lastCard && lastCardValue !== null && (
          <>
            <Separator />
            <div className="flex items-center justify-between">
              <span className="font-medium">Last Card Value:</span>
              <Badge 
                variant={getLastCardBadgeVariant(lastCardValue)}
                className="text-sm font-bold"
              >
                {lastCardValue > 0 ? '+' : ''}{lastCardValue}
              </Badge>
            </div>
          </>
        )}

        {/* Count Guidance */}
        <div className="mt-4 p-3 bg-muted/50 rounded-lg">
          <p className="text-xs text-muted-foreground">
            <strong>Tip:</strong> {' '}
            {trueCount >= 2 
              ? "Favorable count! Consider increasing your bet size."
              : trueCount <= -2
              ? "Unfavorable count. Stick to minimum bets."
              : "Neutral count. Play basic strategy and maintain minimum bet."
            }
          </p>
        </div>
      </CardContent>
    </Card>
  )
}
