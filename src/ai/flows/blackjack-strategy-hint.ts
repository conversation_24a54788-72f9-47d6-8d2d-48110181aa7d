'use server';

/**
 * @fileOverview Provides AI-powered Blackjack strategy hints based on the player's hand and the dealer's visible card.
 *
 * This module handles the communication with the AI model to get intelligent strategy recommendations
 * that consider both basic strategy and advanced playing techniques like card counting implications.
 */

import { ai } from '@/ai/ai-instance';
import { z } from 'genkit';

/**
 * Zod schema defining the input for the Blackjack strategy hint flow.
 * Expanded to include more context for better AI decision-making.
 */
const BlackjackStrategyHintInputSchema = z.object({
  playerHand: z
    .array(z.number())
    .describe('The player hand, represented as an array of card values.'),
  dealerUpCard: z.number().describe('The dealer up card value.'),
  playerHasAce: z
    .boolean()
    .describe('Whether the player hand contains an Ace (value of 11).'),
  // Additional context fields (optional)
  canSplit: z
    .boolean()
    .optional()
    .describe('Whether the player can split their hand (has a pair).'),
  handTotal: z
    .number()
    .optional()
    .describe('The pre-calculated total of the player\'s hand.'),
  gameVariant: z
    .string()
    .optional()
    .describe('Specific game variant rules that might affect strategy.'),
  runningCount: z
    .number()
    .optional()
    .describe('Current running count if card counting is active.'),
  trueCount: z
    .number()
    .optional()
    .describe('True count normalized for remaining decks if card counting is active.'),
});

/**
 * TypeScript type inferred from the input schema.
 */
export type BlackjackStrategyHintInput = z.infer<
  typeof BlackjackStrategyHintInputSchema
>;

/**
 * Zod schema defining the output for the Blackjack strategy hint flow.
 * Enhanced to provide more detailed and structured advice.
 */
const BlackjackStrategyHintOutputSchema = z.object({
  recommendedAction: z
    .enum(['hit', 'stand', 'double', 'split', 'surrender'])
    .describe('The AI recommended optimal action.'),
  advice: z
    .string()
    .describe('Human-readable explanation of the recommended action.'),
  reason: z
    .string()
    .optional()
    .describe('Technical reasoning behind the recommendation.'),
  confidence: z
    .number()
    .min(0)
    .max(1)
    .optional()
    .describe('Confidence level in the recommendation (0-1).'),
  followsBasicStrategy: z
    .boolean()
    .optional()
    .describe('Whether this recommendation follows standard basic strategy.'),
  countingImplication: z
    .string()
    .optional()
    .describe('How card counting affects this decision, if applicable.'),
});

/**
 * TypeScript type inferred from the output schema.
 */
export type BlackjackStrategyHintOutput = z.infer<
  typeof BlackjackStrategyHintOutputSchema
>;

/**
 * Server action function that invokes the AI to get a Blackjack strategy hint.
 *
 * @param input - The player's hand details, dealer's upcard, and optional context information
 * @returns A promise resolving to the AI's recommendation with detailed explanation
 * @throws Error if the AI service fails to provide a valid response
 */
export async function blackjackStrategyHint(
  input: BlackjackStrategyHintInput
): Promise<BlackjackStrategyHintOutput> {
  try {
    // Add performance metrics
    const startTime = performance.now();

    // Prepare the prompt for the AI
    const prompt = `You are a world-class Blackjack strategy expert providing advice to players.

    Based on the player's hand and the dealer's up card, recommend the optimal action following standard basic strategy. Consider additional context if provided.

    Player's hand values: ${JSON.stringify(input.playerHand)}
    Dealer's up card value: ${input.dealerUpCard}
    Player has an Ace counted as 11 (soft hand): ${input.playerHasAce}

    ${input.canSplit ? `Player can split: ${input.canSplit}` : ''}
    ${input.handTotal ? `Hand total: ${input.handTotal}` : ''}
    ${input.gameVariant ? `Game variant: ${input.gameVariant}` : ''}
    ${input.runningCount !== undefined ? `Running count: ${input.runningCount}` : ''}
    ${input.trueCount !== undefined ? `True count: ${input.trueCount}` : ''}

    First determine the correct basic strategy move. Then, if counting information is provided, consider whether the count suggests deviating from basic strategy.

    Provide your recommendation in a structured format with:
    1. The recommended action (hit, stand, double, split, or surrender)
    2. A clear explanation in 15-25 words that a beginner player would understand
    3. Technical reasoning for advanced players (optional)
    4. Your confidence level from 0 to 1
    5. Whether this follows basic strategy
    6. How card counting affects this decision (if count information was provided)

    IMPORTANT: Your response must be a valid JSON object with no additional text before or after. Use the following structure exactly:

    {
      "recommendedAction": "hit", // one of: hit, stand, double, split, surrender
      "advice": "Brief explanation for beginners",
      "reason": "Technical reasoning for advanced players",
      "confidence": 0.9,
      "followsBasicStrategy": true,
      "countingImplication": "How card counting affects this decision"
    }

    Do not include any explanatory text outside the JSON object. The response must be parseable by JSON.parse().
    `;

    // Generate the response using the AI
    const response = await ai.generate(prompt);

    // Parse the JSON response
    let output: BlackjackStrategyHintOutput;
    try {
      // Log the raw response for debugging
      console.log('Raw AI response:', response.text);

      // Try to extract JSON from the response text
      // The AI might return the JSON with additional text before or after
      let jsonText = response.text;

      // Try to find JSON object in the response
      const jsonMatch = response.text.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        jsonText = jsonMatch[0];
      }

      // Try to parse the JSON
      const parsedResponse = JSON.parse(jsonText);
      console.log('Parsed response:', parsedResponse);

      // Validate the response against our schema
      output = {
        recommendedAction: parsedResponse.recommendedAction || 'stand',
        advice: parsedResponse.advice || 'Consider standing as a safe option.',
        reason: parsedResponse.reason,
        confidence: parsedResponse.confidence || 0.7,
        followsBasicStrategy: parsedResponse.followsBasicStrategy !== undefined ?
          parsedResponse.followsBasicStrategy : true,
        countingImplication: parsedResponse.countingImplication
      };

      // Ensure recommendedAction is one of the allowed values
      const validActions = ['hit', 'stand', 'double', 'split', 'surrender'];
      if (!validActions.includes(output.recommendedAction)) {
        console.warn(`Invalid action '${output.recommendedAction}', defaulting to 'stand'`);
        output.recommendedAction = 'stand';
      }
    } catch (parseError) {
      console.error('Error parsing AI response:', parseError);
      console.error('Raw response:', response.text);

      // Provide a fallback response for parsing errors
      output = {
        recommendedAction: 'stand',
        advice: 'Unable to parse AI recommendation. Standing is generally the safest option.',
        confidence: 0.5,
        followsBasicStrategy: false
      };
    }

    // Log performance for monitoring
    const duration = performance.now() - startTime;
    console.log(`AI strategy hint generated in ${duration.toFixed(2)}ms`);

    return output;
  } catch (error) {
    // Enhanced error logging
    console.error('Error getting strategy hint from AI:', error);

    // Log detailed error information for debugging
    if (error instanceof Error) {
      console.error('Error details:', {
        message: error.message,
        stack: error.stack,
        name: error.name
      });
    }

    // Check if it's a network or API key related error
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    if (errorMessage.includes('api key') || errorMessage.includes('authentication')) {
      console.error('API key issue detected. Please check your GOOGLE_GENAI_API_KEY environment variable.');
    } else if (errorMessage.includes('network') || errorMessage.includes('fetch')) {
      console.error('Network issue detected. Please check your internet connection.');
    }

    // Provide a fallback response rather than propagating the error
    return {
      recommendedAction: 'stand',
      advice: 'Unable to get AI recommendation at this time. Standing is generally the safest option.',
      confidence: 0.5,
      followsBasicStrategy: false
    };
  }
}
