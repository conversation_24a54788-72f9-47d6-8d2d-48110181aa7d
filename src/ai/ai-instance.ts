/**
 * @fileOverview Central AI configuration and instance management
 *
 * This module configures the Genkit AI integration for the Blackjack Trainer,
 * with support for environment-specific settings and fallback mechanisms.
 */

import { genkit } from 'genkit';
import { googleAI, gemini20Flash, gemini15Pro } from '@genkit-ai/googleai';

// Development mode flag
const isDevelopment = process.env.NODE_ENV === 'development';

// Default model selection based on environment variable
const defaultModel = process.env.NEXT_PUBLIC_AI_MODEL === 'googleai/gemini-1.5-pro'
  ? gemini15Pro
  : gemini20Flash;

/**
 * Initialize the Google AI provider with API key
 * @returns Configured Google AI provider or null if API key is missing
 */
function initGoogleAI() {
  // Check if API key is available
  if (!process.env.GOOGLE_GENAI_API_KEY) {
    console.warn('⚠️ No Google Gemini API key found in environment variables');
    return null;
  }

  try {
    console.log('🔑 Google Gemini API key found, length:', process.env.GOOGLE_GENAI_API_KEY.length);

    // Initialize Google AI provider with API key
    const provider = googleAI({
      apiKey: process.env.GOOGLE_GENAI_API_KEY
    });

    console.log('✅ Google AI provider initialized successfully');
    return provider;
  } catch (error) {
    console.error('❌ Failed to initialize Google AI provider:', error);
    return null;
  }
}

// Initialize the Google AI provider
const googleAIProvider = initGoogleAI();

// Configure plugins array for Genkit
const plugins = [];
if (googleAIProvider) {
  plugins.push(googleAIProvider);
  console.log(`🧠 Initialized ${plugins.length} AI provider(s)`);
} else if (isDevelopment) {
  console.warn('⚠️ No AI provider API keys found. AI features will be unavailable.');
}

/**
 * Create and configure the Genkit AI instance
 */
export const ai = genkit({
  plugins,
  model: defaultModel,
});

/**
 * Log AI-related analytics event
 *
 * @param eventName - Name of the event to log
 * @param data - Additional data related to the event
 */
export function logAIEvent(eventName: string, data: Record<string, any> = {}) {
  // Check if logging is enabled
  const loggingEnabled = process.env.AI_LOGGING === 'true' || isDevelopment;
  if (!loggingEnabled) return;

  const eventData = {
    event: eventName,
    timestamp: new Date().toISOString(),
    model: typeof defaultModel === 'string' ? defaultModel : 'gemini-2.0-flash',
    ...data
  };

  // In development mode, log to console
  if (isDevelopment) {
    console.log(`🧠 AI Event: ${eventName}`, eventData);
  }

  // In production, these could be sent to analytics services
  // Example: analytics.trackEvent(eventName, eventData);
}

/**
 * Get current AI system configuration
 * Useful for components that need to display AI-related status
 */
export function getAIConfig() {
  return {
    model: typeof defaultModel === 'string' ? defaultModel : 'gemini-2.0-flash',
    isEnabled: plugins.length > 0,
    isDevelopment
  };
}
