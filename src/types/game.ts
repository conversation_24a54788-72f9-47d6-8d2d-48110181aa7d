/**
 * @fileOverview Type definitions for Stack Advantage Blackjack Trainer game
 * Provides types for game state, actions, cards, and player interactions
 */

import { COUNTING_SYSTEMS } from '@/lib/constants';

/**
 * Represents a playing card with suit and value
 */
export type Card = string; // Format: "SV" where S is suit (C, D, H, S) and V is value (2-10, J, Q, K, A)

/**
 * Valid game status states
 */
export type GameStatus = 'betting' | 'playing' | 'ended' | 'idle';

/**
 * Game outcome determination
 */
export type GameOutcome = 'player' | 'dealer' | 'tie' | null;

/**
 * Player actions available during the game
 */
export type PlayerAction = 'hit' | 'stand' | 'double' | 'split' | 'surrender';

/**
 * Card counting system keys
 */
export type CountingSystemKey = keyof typeof COUNTING_SYSTEMS;

/**
 * Hand calculation result with total value and soft status
 */
export interface HandCalculation {
  /** The total point value of the hand */
  total: number;
  /** Whether the hand is "soft" (contains an Ace counted as 11) */
  isSoft: boolean;
}

/**
 * Training mode options for the game
 */
export type GameMode = 'practice' | 'challenge';

/**
 * Result of count verification in challenge mode
 */
export interface CountVerificationResult {
  /** Player's count input */
  playerCount: number;
  /** Actual count in the game */
  actualCount: number;
  /** Accuracy percentage (0-100) */
  accuracy: number;
  /** Whether the count was correct */
  isCorrect: boolean;
}

/**
 * Game settings configuration
 */
export interface GameSettings {
  /** Number of card decks in the shoe */
  deckCount: number;
  /** Whether dealer must stand on soft 17 */
  dealerStandsOnSoft17: boolean;
  /** Whether doubling after split is allowed */
  doubleAfterSplit: boolean;
  /** Whether surrender is allowed */
  surrenderAllowed: boolean;
  /** Whether player can re-split Aces */
  resplitAcesAllowed: boolean;
  /** Maximum number of times a hand can be split */
  maxSplits: number;
  /** Payout ratio for blackjack (typically 1.5 for 3:2 payout) */
  blackjackPayout: number;
  /** Card counting system being used */
  countingSystem: CountingSystemKey;
  /** Starting bankroll amount */
  initialBankroll: number;
  /** Minimum allowed bet */
  minimumBet: number;
  /** Maximum allowed bet */
  maximumBet: number;
  /** Current game mode */
  gameMode: GameMode;
  /** Whether to show count information in practice mode */
  showCountInPractice: boolean;
  /** Whether to allow hints in challenge mode */
  allowHintsInChallenge: boolean;
  /** How often to verify count with player */
  countVerificationFrequency: 'never' | 'round' | 'shuffle';
}

/**
 * Player hand information
 */
export interface PlayerHand {
  /** Array of cards in the hand */
  cards: Card[];
  /** Total value of the hand */
  total: number;
  /** Whether the hand is soft */
  isSoft: boolean;
  /** Current bet amount on this hand */
  bet: number;
  /** Whether this hand has been completed */
  isDone: boolean;
  /** Whether this hand has busted */
  isBusted: boolean;
  /** Whether this hand is a blackjack */
  isBlackjack: boolean;
  /** Whether this hand can be split */
  canSplit?: boolean;
  /** Whether this hand can be doubled */
  canDouble?: boolean;
}

/**
 * Dealer hand information
 */
export interface DealerHand {
  /** Array of cards in the hand */
  cards: Card[];
  /** Total value of the hand when all cards are revealed */
  total: number;
  /** Whether the hand is soft */
  isSoft: boolean;
  /** Whether the first card is revealed */
  isRevealed: boolean;
  /** Whether the dealer has blackjack */
  isBlackjack: boolean;
}

/**
 * Current state of a game round
 */
export interface GameState {
  /** Current player hands (multiple in case of splits) */
  playerHands: PlayerHand[];
  /** Current active hand index (for splits) */
  activeHandIndex: number;
  /** Dealer's hand */
  dealerHand: DealerHand;
  /** Current game status */
  gameStatus: GameStatus;
  /** Game outcome if finished */
  outcome: GameOutcome;
  /** Player's current bankroll/stack */
  bankroll: number;
  /** Cards remaining in the shoe */
  cardsRemaining: number;
  /** Current count (for card counting practice) */
  count: number;
  /** True count (count / decks remaining) */
  trueCount: number;
  /** Result message to display to player */
  resultMessage: string;
  /** Current minimum bet based on true count and bankroll */
  recommendedBet: number;
}

/**
 * History of a completed game round
 */
export interface GameRound {
  /** Unique ID for the round */
  id: string;
  /** Player's hands in this round */
  playerHands: PlayerHand[];
  /** Dealer's final hand */
  dealerHand: DealerHand;
  /** Net winnings/losses for this round */
  netResult: number;
  /** Count at the start of the round */
  startingCount: number;
  /** Count at the end of the round */
  endingCount: number;
  /** Whether the player followed basic strategy */
  followedBasicStrategy: boolean;
  /** Whether the player made the correct betting decision */
  correctBettingDecision: boolean;
  /** Timestamp when the round was completed */
  timestamp: number;
}

/**
 * Parameters for game initialization
 */
export interface GameInitParams {
  /** Initial game settings */
  settings?: Partial<GameSettings>;
  /** Start with specific bankroll */
  initialBankroll?: number;
}

/**
 * Result of a completed game 
 */
export interface GameResult {
  /** Text message describing the result */
  result: string;
  /** Amount won or lost */
  winnings: number;
  /** Reason for the outcome */
  reason?: string;
  /** Whether the player made the optimal decision */
  optimalPlay?: boolean;
}

/**
 * Game action result
 */
export interface ActionResult {
  /** Whether the action was successful */
  success: boolean;
  /** Error message if action failed */
  error?: string;
  /** Updated game state */
  gameState: GameState;
}

/**
 * AI hint for the current game state
 */
export interface GameHint {
  /** The recommended action */
  recommendedAction: PlayerAction;
  /** Explanation of why this action is recommended */
  explanation: string;
  /** Confidence level in the recommendation (0-1) */
  confidence: number;
}

/**
 * Strategy training metrics
 */
export interface TrainingMetrics {
  /** Number of correct decisions made */
  correctDecisions: number;
  /** Total decisions made */
  totalDecisions: number;
  /** Number of correct betting decisions */
  correctBettingDecisions: number;
  /** Total betting decisions */
  totalBettingDecisions: number;
  /** Accuracy percentage */
  accuracy: number;
  /** Net profit/loss */
  netProfit: number;
  /** Number of rounds played */
  roundsPlayed: number;
}