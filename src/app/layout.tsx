/**
 * @fileOverview Root layout component for the Next.js application.
 * Sets up the HTML structure, applies global styles, fonts, and metadata.
 */
import type {Metadata} from 'next';
import {<PERSON>eist, <PERSON>eist_Mono} from 'next/font/google';
import './globals.css';

/**
 * Geist Sans font configuration.
 */
const geistSans = Geist({
  variable: '--font-geist-sans',
  subsets: ['latin'],
});

/**
 * Geist Mono font configuration.
 */
const geistMono = Geist_Mono({
  variable: '--font-geist-mono',
  subsets: ['latin'],
});

/**
 * Default metadata for the application.
 */
export const metadata: Metadata = {
  title: 'Stack Advantage - Blackjack Trainer', // Updated title
  description: 'Learn and practice Hi-Lo card counting in Blackjack.', // Updated description
};

/**
 * Root layout component.
 * Wraps all pages, applies global fonts and styles.
 * Sets the HTML lang attribute and enables dark mode by default.
 * @param {object} props - Component props.
 * @param {React.ReactNode} props.children - The child elements to render within the layout.
 * @returns {JSX.Element} The root HTML structure.
 */
export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" className="dark"> 
      <body className={`${geistSans.variable} ${geistMono.variable} antialiased`}>
        {children}
      </body>
    </html>
  );
}
