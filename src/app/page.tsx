'use client';

import { useState, useEffect } from 'react';
import { blackjackStrategyHint } from '@/ai/flows/blackjack-strategy-hint';
import { Button } from '@/components/ui/button';
import { PlayingCard, ModeSelector, CountDisplay, CountVerification } from '@/components';
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { SparklesIcon } from 'lucide-react';
import { useBlackjack } from '@/hooks/useBlackjack';
import { 
  DEFAULT_GAME_SETTINGS, 
  PLAYER_ACTIONS 
} from '@/lib/constants';

import type {
  GameMode,
  PlayerHand,
  DealerHand,
  GameStatus,
  CountVerificationResult
} from '@/types/game';

/**
 * Home Page Component - Main game interface for the Stack Advantage Blackjack Trainer
 * 
 * This component renders the training mode interface with practice and challenge modes.
 * In practice mode, counts are visible and strategy hints are available.
 * In challenge mode, counts are hidden to test proficiency.
 * 
 * @returns React component with interactive blackjack game UI
 */
export default function Home() {
  // Use the blackjack hook for game state management
  const {
    gameState,
    gameMode,
    setGameMode,
    isCountVisible,
    placeBet,
    hit,
    stand,
    doubleDown,
    split,
    startNewRound,
    verifyCount
  } = useBlackjack();
  
  // UI state for player interactions
  const [betAmount, setBetAmount] = useState<number>(DEFAULT_GAME_SETTINGS.minimumBet);
  const [betInputError, setBetInputError] = useState<string>("");
  
  // AI hint functionality (only available in practice mode)
  const [aiHint, setAiHint] = useState<string | null>(null);
  const [showAiHint, setShowAiHint] = useState<boolean>(false);
  const [isHintLoading, setIsHintLoading] = useState<boolean>(false);
  
  // Training metrics
  const [correctDecisions, setCorrectDecisions] = useState<number>(0);
  const [totalDecisions, setTotalDecisions] = useState<number>(0);
  
  // Count verification dialog state (challenge mode)
  const [showCountVerification, setShowCountVerification] = useState<boolean>(false);

  // Computed properties
  const activeHand: PlayerHand | undefined = gameState?.playerHands[gameState?.activeHandIndex || 0];
  const dealerHand: DealerHand | undefined = gameState?.dealerHand;
  const currentGameStatus: GameStatus = gameState?.gameStatus || 'idle';
  const canDoubleDown = activeHand?.canDouble ?? false;
  const canSplit = activeHand?.canSplit ?? false;
  const playerIsBusted = activeHand?.isBusted ?? false;
  const playerTotal = activeHand?.total ?? 0;
  const playerIsSoft = activeHand?.isSoft ?? false;
  const dealerTotal = dealerHand?.total ?? 0;
  const dealerIsSoft = dealerHand?.isSoft ?? false;
  const currentBankroll = gameState?.bankroll ?? DEFAULT_GAME_SETTINGS.initialBankroll;
  const resultMessage = gameState?.resultMessage ?? '';
  
  /**
   * Handles changes to the bet input field.
   * Validates the input to ensure it's a positive integer and not greater than available bankroll.
   * @param event - The input change event.
   */
  const handleBetInputChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const value = event.target.value;
    
    if (value === "" || /^[1-9]\d*$/.test(value)) {
      const numValue = value === "" ? 0 : parseInt(value, 10);
      
      if (numValue > currentBankroll) {
        setBetInputError(`Cannot bet more than available bankroll ($${currentBankroll})`);
        setBetAmount(currentBankroll);
      } else if (numValue < DEFAULT_GAME_SETTINGS.minimumBet) {
        setBetInputError(`Minimum bet is $${DEFAULT_GAME_SETTINGS.minimumBet}`);
        setBetAmount(DEFAULT_GAME_SETTINGS.minimumBet);
      } else if (numValue > DEFAULT_GAME_SETTINGS.maximumBet) {
        setBetInputError(`Maximum bet is $${DEFAULT_GAME_SETTINGS.maximumBet}`);
        setBetAmount(DEFAULT_GAME_SETTINGS.maximumBet);
      } else {
        setBetInputError("");
        setBetAmount(numValue);
      }
    } else if (value === "0") {
      setBetInputError(`Minimum bet is $${DEFAULT_GAME_SETTINGS.minimumBet}`);
      setBetAmount(0);
    } else {
      setBetInputError("Invalid bet amount");
    }
  };
  /**
   * Places a bet and deals cards to start a new round.
   */
  const placeBetAndDeal = () => {
    setBetAmount(betAmount); // Set the bet amount in the hook
    const result = placeBet();
    
    if (result.success) {
      setAiHint(null);
      setIsHintLoading(false);
    } else {
      setBetInputError(result.error || "Error placing bet");
    }
  };

  /**
   * Checks if the player's action matches the AI hint (if shown) and updates
   * the correct/total decision counters.
   * @param playerAction - The action the player took (e.g., 'Hit', 'Stand').
   */
  const checkDecision = (playerAction: string) => {
    if (showAiHint && aiHint && aiHint !== 'Error getting hint' && aiHint !== 'Hint not available') {
      setTotalDecisions(prev => prev + 1);
      if (aiHint.toLowerCase().includes(playerAction.toLowerCase())) {
        setCorrectDecisions(prev => prev + 1);
      }
    }
  };

  /**
   * Wrapper for hit action with decision tracking
   */
  const handleHit = () => {
    if (currentGameStatus !== 'playing') return;
    
    checkDecision(PLAYER_ACTIONS.HIT);
    
    const result = hit();
    
    if (result.success) {
      setAiHint(null);
      setIsHintLoading(false);
      
      if (result.gameState.gameStatus === 'playing' && showAiHint && gameMode === 'practice') {
        getAiHint();
      }
    }
  };

  /**
   * Wrapper for stand action with decision tracking
   */
  const handleStand = () => {
    if (currentGameStatus !== 'playing') return;
    
    checkDecision(PLAYER_ACTIONS.STAND);
    
    const result = stand();
    
    if (result.success) {
      setAiHint(null);
      setIsHintLoading(false);
    }
  };

  /**
   * Wrapper for double down action with decision tracking
   */
  const handleDoubleDown = () => {
    if (currentGameStatus !== 'playing' || !canDoubleDown) return;
    
    checkDecision(PLAYER_ACTIONS.DOUBLE);
    
    const result = doubleDown();
    
    if (result.success) {
      setAiHint(null);
      setIsHintLoading(false);
    }
  };

  /**
   * Wrapper for split action with decision tracking
   */
  const handleSplit = () => {
    if (currentGameStatus !== 'playing' || !canSplit) return;
    
    checkDecision(PLAYER_ACTIONS.SPLIT);
    
    const result = split();
    
    if (result.success) {
      setAiHint(null);
      setIsHintLoading(false);
      
      if (result.gameState.gameStatus === 'playing' && showAiHint && gameMode === 'practice') {
        getAiHint();
      }
    } else {
      alert(result.error || "Split functionality not yet fully implemented.");
    }
  };

  /**
   * Handles new round with UI reset
   */
  const handleNewRound = () => {
    setBetAmount(DEFAULT_GAME_SETTINGS.minimumBet);
    setBetInputError("");
    setAiHint(null);
    setIsHintLoading(false);
    startNewRound();
  };

  /**
   * Fetches a basic strategy hint from the AI backend.
   * Only available in practice mode.
   */
  const getAiHint = async () => {
    if (!gameState || !activeHand || !dealerHand || 
        currentGameStatus !== 'playing' || 
        dealerHand.cards.length === 0 || 
        activeHand.cards.length === 0 || 
        playerTotal > 21 ||
        gameMode !== 'practice') {
        setAiHint(null);
        setIsHintLoading(false);
        return;
    }

    const dealerUpCard = dealerHand.cards[0];
    if (!dealerUpCard) return;

    // Extract card values for the AI hint
    const extractCardValue = (card: string): number => {
      const value = card.substring(1);
      switch (value) {
        case 'A': return 11;
        case 'K':
        case 'Q':
        case 'J': return 10;
        default: return parseInt(value);
      }
    };

    const dealerUpCardValue = extractCardValue(dealerUpCard);
    const playerHandValues = activeHand.cards.map(extractCardValue);

    setAiHint(null);
    setIsHintLoading(true);
    
    try {
      const hint = await blackjackStrategyHint({
        playerHand: playerHandValues,
        dealerUpCard: dealerUpCardValue,
        playerHasAce: playerIsSoft
      });
      
      setAiHint(hint.advice);
    } catch (error) {
      console.error('Failed to get AI hint:', error);
      setAiHint('Error getting hint');
    } finally {
      setIsHintLoading(false);
    }
  };  /**
   * Handles count verification submission in challenge mode
   */
  const handleCountVerification = (playerCount: number): CountVerificationResult => {
    const result = verifyCount(playerCount);
    
    if (result.isCorrect) {
      setCorrectDecisions(prev => prev + 1);
    }
    setTotalDecisions(prev => prev + 1);
    
    return result;
  };

  // Request AI hint when appropriate (practice mode only)
  useEffect(() => {
    if (showAiHint && 
        gameMode === 'practice' &&
        currentGameStatus === 'playing' && 
        !aiHint && 
        !isHintLoading && 
        activeHand && 
        activeHand.total <= 21) {
      getAiHint();
    }
    
    if (!showAiHint || gameMode !== 'practice') {
      setAiHint(null);
    }
  }, [showAiHint, currentGameStatus, activeHand?.cards, gameMode]);

  // Handle initial checks for blackjack or special situations
  useEffect(() => {
    if (gameState && 
        currentGameStatus === 'playing' && 
        activeHand && 
        dealerHand && 
        activeHand.cards.length === 2 && 
        dealerHand.cards.length === 2 &&
        gameMode === 'practice') {
        
        if (showAiHint && !activeHand.isBlackjack) {
          getAiHint();
        }
    }
  }, [currentGameStatus, activeHand?.cards, gameMode]);

  return (
    <main className="flex flex-col items-center justify-center min-h-screen bg-black text-gray-200 p-4">
      <h1 className="text-4xl font-bold mb-4">StackJack</h1>

      {/* Training Mode Selector */}
      <div className="mb-6">
        <ModeSelector 
          currentMode={gameMode}
          onModeChange={setGameMode}
        />
      </div>

      {/* Display Game State & Stats */}
      <div className="mb-4 text-center space-y-2">
        <Badge variant="secondary" className="text-lg px-4 py-1 bg-gray-800 text-gray-200 border-gray-700">
          Stack: ${currentBankroll}
        </Badge>
        
        <div className="text-sm text-gray-400">
          Correct Decisions: {correctDecisions} / {totalDecisions}
          {totalDecisions > 0 && ` (${((correctDecisions / totalDecisions) * 100).toFixed(0)}%)`}
        </div>
        
        {(currentGameStatus === 'idle' || currentGameStatus === 'betting') ? (
          <div className="flex flex-col items-center gap-2 mt-4">
            <span className="text-xl mb-2">Place Your Bet</span>
            <div className="flex items-center gap-2">
              <Button 
                variant="outline" 
                size="sm" 
                onClick={() => setBetAmount(Math.max(DEFAULT_GAME_SETTINGS.minimumBet, betAmount - 10))} 
                disabled={betAmount <= DEFAULT_GAME_SETTINGS.minimumBet}
              >
                -10
              </Button>
              <Input 
                type="number"
                value={betAmount === 0 ? "" : betAmount}
                onChange={handleBetInputChange}
                placeholder="Bet Amount"
                min={DEFAULT_GAME_SETTINGS.minimumBet}
                max={currentBankroll}
                className="w-24 text-center bg-gray-800 border-gray-700 text-white placeholder-gray-400 focus:ring-blue-500 focus:border-blue-500 rounded-md"
              />
              <Button 
                variant="outline" 
                size="sm" 
                onClick={() => setBetAmount(Math.min(currentBankroll, betAmount + 10))} 
                disabled={betAmount >= currentBankroll}
              >
                +10
              </Button>
            </div>
            
            {betInputError && <p className="text-red-400 text-sm mt-1">{betInputError}</p>}
            
            <Button 
              onClick={placeBetAndDeal} 
              disabled={betAmount <= 0 || betAmount > currentBankroll || !!betInputError}
              className="mt-2"
              variant="secondary"
            >
              Deal
            </Button>
          </div>
        ) : (
          <div className="mt-2">
            Bet: ${activeHand?.bet || 0}
          </div>
        )}
      </div>      {/* Count Display - Practice Mode Only */}
      {isCountVisible() && gameState && (
        <div className="mb-4">
          <CountDisplay 
            isVisible={true}
            runningCount={gameState.count}
            trueCount={gameState.trueCount}
            remainingDecks={Math.ceil(gameState.cardsRemaining / 52)}
          />
        </div>
      )}

      {/* Dealer's Hand */}
      {currentGameStatus !== 'idle' && currentGameStatus !== 'betting' && dealerHand && (
        <div className="mb-8">
          <h2 className="text-center mb-2 text-lg font-semibold text-gray-400">
            Dealer's Hand ({(currentGameStatus === 'ended' || playerIsBusted) ? 
              `${dealerTotal}${dealerIsSoft ? ' soft' : ''}` : ' ?'})
          </h2>
          <div className="flex justify-center">
            {dealerHand.cards.map((card, index) => (
              <div key={index} className="mx-[-1rem]">
                <PlayingCard 
                  card={card} 
                  faceUp={index === 0 || dealerHand.isRevealed || currentGameStatus === 'ended' || playerIsBusted} 
                />
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Player's Hand(s) */}
      {currentGameStatus !== 'idle' && currentGameStatus !== 'betting' && gameState && (
        <div className="mb-8">
          {gameState.playerHands.map((hand: PlayerHand, handIndex: number) => (
            <div 
              key={handIndex} 
              className={`mb-4 ${handIndex === gameState.activeHandIndex && currentGameStatus === 'playing' ? 'ring-2 ring-blue-500 p-2 rounded' : ''}`}
            >
              <h2 className="text-center mb-2 text-lg font-semibold text-gray-400">
                {gameState.playerHands.length > 1 ? `Hand ${handIndex + 1}` : 'Your Hand'} ({hand.total}{hand.isSoft ? ' soft' : ''})
                {hand.isBusted && <span className="text-red-500 ml-2">(Busted)</span>}
              </h2>
              <div className="flex justify-center">
                {hand.cards.map((card: string, cardIndex: number) => (
                  <div key={cardIndex} className="mx-[-1rem]">
                    <PlayingCard card={card} faceUp={true} />
                  </div>
                ))}
              </div>
            </div>
          ))}
        </div>
      )}

      {/* Game Result */}
      {resultMessage && (
        <div className="text-2xl font-bold mb-4 text-center">
          {resultMessage}
        </div>
      )}

      {/* Game Actions */}
      {currentGameStatus === 'playing' && activeHand && !activeHand.isBusted && !activeHand.isDone && (
        <div className="flex gap-4 mb-4">
          <Button onClick={handleHit} disabled={isHintLoading} variant="secondary">Hit</Button>
          <Button onClick={handleStand} disabled={isHintLoading} variant="secondary">Stand</Button>
          
          {canDoubleDown && (
            <Button onClick={handleDoubleDown} disabled={isHintLoading} variant="secondary">Double Down</Button>
          )}
          
          {canSplit && (
            <Button onClick={handleSplit} disabled={isHintLoading} variant="secondary">Split</Button>
          )}
          
          {/* Count Verification Button - Challenge Mode Only */}
          {gameMode === 'challenge' && (
            <Button 
              onClick={() => setShowCountVerification(true)} 
              variant="outline"
              className="border-yellow-500 text-yellow-500 hover:bg-yellow-500 hover:text-black"
            >
              Verify Count
            </Button>
          )}
        </div>
      )}
      
      {currentGameStatus === 'ended' && (
        <Button 
          onClick={handleNewRound} 
          className="mb-4" 
          variant="secondary"
        >
          Place New Bet
        </Button>
      )}

      {/* AI Strategy Hint - Practice Mode Only */}
      {gameMode === 'practice' && currentGameStatus === 'playing' && activeHand && activeHand.total <= 21 && !activeHand.isDone && (
        <div className="mt-4 flex flex-col items-center">
          <div className="flex items-center">
            <label htmlFor="ai-hint-toggle" className="flex items-center cursor-pointer">
              <div className="relative">
                <input 
                  type="checkbox" 
                  id="ai-hint-toggle" 
                  className="sr-only peer" 
                  checked={showAiHint} 
                  onChange={() => setShowAiHint(!showAiHint)} 
                  disabled={isHintLoading} 
                />
                <div className="w-11 h-6 bg-gray-600 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-0.5 after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600"></div>
              </div>
              <span className="ml-3 text-sm font-medium text-gray-300">Show AI Hint</span>
            </label>
          </div>

          {showAiHint && (
            <div className="mt-2">
              <Badge variant="outline" className="border-gray-700 text-gray-300 bg-gray-800">
                {isHintLoading ? 
                  "Getting hint..." : 
                  aiHint ? 
                    <>AI Hint: {aiHint} <SparklesIcon className="ml-1 inline-block h-4 w-4 text-yellow-400" /></> : 
                    "Hint not available"
                }
              </Badge>
            </div>
          )}
        </div>
      )}      {/* Count Verification Dialog - Challenge Mode */}
      {showCountVerification && gameState && (
        <CountVerification
          isOpen={showCountVerification}
          onClose={() => setShowCountVerification(false)}
          onVerify={handleCountVerification}
        />
      )}
    </main>
  );
}