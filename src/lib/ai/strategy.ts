/**
 * @fileOverview Blackjack Strategy AI advisor implementation
 * 
 * This module provides both AI-powered and rules-based strategy advice for
 * Blackjack gameplay, with fallback mechanisms and performance optimization.
 */

import { blackjackStrategyHint } from '@/ai/flows/blackjack-strategy-hint';
import type { BlackjackStrategyHintInput, BlackjackStrategyHintOutput } from '@/ai/flows/blackjack-strategy-hint';
import { logAIEvent } from '@/ai/ai-instance';
import type { PlayerAction, HandCalculation, GameSettings, PlayerHand } from '@/types/game';

/**
 * Extended game settings with AI hint option
 */
interface ExtendedGameSettings extends GameSettings {
  /** Whether AI-powered hints are enabled */
  aiHints?: boolean;
}

/**
 * Strategy advice result returned by the strategy advisor
 */
export interface StrategyAdvice {
  /** The recommended action to take */
  action: PlayerAction;
  /** Human-readable explanation of the advice */
  advice: string;
  /** Confidence level in the recommendation (0-1) */
  confidence?: number;
  /** Whether this follows basic strategy rules */
  isBasicStrategy: boolean;
  /** Source of the advice (ai, rules, fallback) */
  source: 'ai' | 'rules' | 'fallback';
}

/**
 * Basic strategy rules lookup tables
 * These are simplified versions of traditional basic strategy charts
 */
const BASIC_STRATEGY = {
  // Hard totals (player doesn't have an ace counting as 11)
  hard: {
    // Key: player total, Value: object mapping dealer upcard to action
    8: { 2: 'hit', 3: 'hit', 4: 'hit', 5: 'hit', 6: 'hit', 7: 'hit', 8: 'hit', 9: 'hit', 10: 'hit', 11: 'hit' },
    9: { 2: 'hit', 3: 'double', 4: 'double', 5: 'double', 6: 'double', 7: 'hit', 8: 'hit', 9: 'hit', 10: 'hit', 11: 'hit' },
    10: { 2: 'double', 3: 'double', 4: 'double', 5: 'double', 6: 'double', 7: 'double', 8: 'double', 9: 'double', 10: 'hit', 11: 'hit' },
    11: { 2: 'double', 3: 'double', 4: 'double', 5: 'double', 6: 'double', 7: 'double', 8: 'double', 9: 'double', 10: 'double', 11: 'hit' },
    12: { 2: 'hit', 3: 'hit', 4: 'stand', 5: 'stand', 6: 'stand', 7: 'hit', 8: 'hit', 9: 'hit', 10: 'hit', 11: 'hit' },
    13: { 2: 'stand', 3: 'stand', 4: 'stand', 5: 'stand', 6: 'stand', 7: 'hit', 8: 'hit', 9: 'hit', 10: 'hit', 11: 'hit' },
    14: { 2: 'stand', 3: 'stand', 4: 'stand', 5: 'stand', 6: 'stand', 7: 'hit', 8: 'hit', 9: 'hit', 10: 'hit', 11: 'hit' },
    15: { 2: 'stand', 3: 'stand', 4: 'stand', 5: 'stand', 6: 'stand', 7: 'hit', 8: 'hit', 9: 'hit', 10: 'hit', 11: 'hit' },
    16: { 2: 'stand', 3: 'stand', 4: 'stand', 5: 'stand', 6: 'stand', 7: 'hit', 8: 'hit', 9: 'hit', 10: 'hit', 11: 'hit' },
    17: { 2: 'stand', 3: 'stand', 4: 'stand', 5: 'stand', 6: 'stand', 7: 'stand', 8: 'stand', 9: 'stand', 10: 'stand', 11: 'stand' },
  },
  
  // Soft totals (player has an ace counting as 11)
  soft: {
    13: { 2: 'hit', 3: 'hit', 4: 'hit', 5: 'double', 6: 'double', 7: 'hit', 8: 'hit', 9: 'hit', 10: 'hit', 11: 'hit' },
    14: { 2: 'hit', 3: 'hit', 4: 'hit', 5: 'double', 6: 'double', 7: 'hit', 8: 'hit', 9: 'hit', 10: 'hit', 11: 'hit' },
    15: { 2: 'hit', 3: 'hit', 4: 'double', 5: 'double', 6: 'double', 7: 'hit', 8: 'hit', 9: 'hit', 10: 'hit', 11: 'hit' },
    16: { 2: 'hit', 3: 'hit', 4: 'double', 5: 'double', 6: 'double', 7: 'hit', 8: 'hit', 9: 'hit', 10: 'hit', 11: 'hit' },
    17: { 2: 'hit', 3: 'double', 4: 'double', 5: 'double', 6: 'double', 7: 'hit', 8: 'hit', 9: 'hit', 10: 'hit', 11: 'hit' },
    18: { 2: 'stand', 3: 'double', 4: 'double', 5: 'double', 6: 'double', 7: 'stand', 8: 'stand', 9: 'hit', 10: 'hit', 11: 'hit' },
    19: { 2: 'stand', 3: 'stand', 4: 'stand', 5: 'stand', 6: 'stand', 7: 'stand', 8: 'stand', 9: 'stand', 10: 'stand', 11: 'stand' },
    20: { 2: 'stand', 3: 'stand', 4: 'stand', 5: 'stand', 6: 'stand', 7: 'stand', 8: 'stand', 9: 'stand', 10: 'stand', 11: 'stand' },
    21: { 2: 'stand', 3: 'stand', 4: 'stand', 5: 'stand', 6: 'stand', 7: 'stand', 8: 'stand', 9: 'stand', 10: 'stand', 11: 'stand' },
  },
  
  // Pairs (player has two cards of the same value)
  pairs: {
    2: { 2: 'split', 3: 'split', 4: 'split', 5: 'split', 6: 'split', 7: 'split', 8: 'hit', 9: 'hit', 10: 'hit', 11: 'hit' },
    3: { 2: 'split', 3: 'split', 4: 'split', 5: 'split', 6: 'split', 7: 'split', 8: 'hit', 9: 'hit', 10: 'hit', 11: 'hit' },
    4: { 2: 'hit', 3: 'hit', 4: 'hit', 5: 'split', 6: 'split', 7: 'hit', 8: 'hit', 9: 'hit', 10: 'hit', 11: 'hit' },
    5: { 2: 'double', 3: 'double', 4: 'double', 5: 'double', 6: 'double', 7: 'double', 8: 'double', 9: 'double', 10: 'hit', 11: 'hit' },
    6: { 2: 'split', 3: 'split', 4: 'split', 5: 'split', 6: 'split', 7: 'hit', 8: 'hit', 9: 'hit', 10: 'hit', 11: 'hit' },
    7: { 2: 'split', 3: 'split', 4: 'split', 5: 'split', 6: 'split', 7: 'split', 8: 'hit', 9: 'hit', 10: 'hit', 11: 'hit' },
    8: { 2: 'split', 3: 'split', 4: 'split', 5: 'split', 6: 'split', 7: 'split', 8: 'split', 9: 'split', 10: 'split', 11: 'split' },
    9: { 2: 'split', 3: 'split', 4: 'split', 5: 'split', 6: 'split', 7: 'stand', 8: 'split', 9: 'split', 10: 'stand', 11: 'stand' },
    10: { 2: 'stand', 3: 'stand', 4: 'stand', 5: 'stand', 6: 'stand', 7: 'stand', 8: 'stand', 9: 'stand', 10: 'stand', 11: 'stand' },
    11: { 2: 'split', 3: 'split', 4: 'split', 5: 'split', 6: 'split', 7: 'split', 8: 'split', 9: 'split', 10: 'split', 11: 'split' },
  },
} as const;

/**
 * Human-readable explanations for strategy recommendations
 */
const ACTION_EXPLANATIONS: Record<PlayerAction, string> = {
  hit: "Take another card to improve your hand.",
  stand: "Keep your current hand and end your turn.",
  double: "Double your bet and receive exactly one more card.",
  split: "Split your pair into two separate hands, each with the same bet.",
  surrender: "Give up half your bet and end the hand immediately."
};

/**
 * Utility class providing Blackjack strategy advice using both AI and rule-based approaches
 */
export class StrategyAdvisor {
  /**
   * Get a strategy hint for the current game state
   * 
   * @param playerHand - Array of card values in the player's hand
   * @param dealerUpCard - The dealer's face-up card value
   * @param playerCalc - Calculated properties of the player's hand
   * @param gameSettings - Optional game settings that might affect strategy
   * @param options - Additional options for strategy generation
   * @returns Promise resolving to strategy advice
   */
  static async getHint(
    playerHand: number[],
    dealerUpCard: number,
    playerCalc: HandCalculation,
    gameSettings?: Partial<ExtendedGameSettings>,
    options: {
      useAI?: boolean;
      runningCount?: number;
      trueCount?: number;
      canSplit?: boolean;
    } = {}
  ): Promise<StrategyAdvice> {
    const startTime = performance.now();
    
    // Determine if we should use AI based on settings and availability
    const useAI = options.useAI !== false && 
                  (gameSettings?.aiHints !== false);
    
    try {
      if (useAI) {
        // Try to get AI-powered hint first
        const aiHint = await this.getAIHint(
          playerHand,
          dealerUpCard,
          playerCalc,
          options.runningCount,
          options.trueCount,
          options.canSplit
        );
        
        logAIEvent('strategy_hint_success', {
          duration: performance.now() - startTime,
          handType: playerCalc.isSoft ? 'soft' : options.canSplit ? 'pair' : 'hard',
          playerTotal: playerCalc.total,
          dealerCard: dealerUpCard,
          action: aiHint.action
        });
        
        return aiHint;
      }
    } catch (error) {
      console.error('AI strategy hint failed, falling back to rules', error);
      logAIEvent('strategy_hint_failure', {
        error: error instanceof Error ? error.message : 'Unknown error',
        duration: performance.now() - startTime
      });
    }
    
    // Fallback to rules-based hint
    const rulesHint = this.getRulesBasedHint(
      playerCalc,
      dealerUpCard,
      options.canSplit
    );
    
    return rulesHint;
  }
  
  /**
   * Get an AI-powered strategy hint
   * 
   * @param playerHand - Array of card values in the player's hand
   * @param dealerUpCard - The dealer's face-up card value
   * @param playerCalc - Calculated properties of the player's hand
   * @param runningCount - Optional current running count for card counting
   * @param trueCount - Optional true count (running count / decks remaining)
   * @param canSplit - Whether the hand can be split
   * @returns Promise resolving to strategy advice
   * @throws Error if AI service fails
   */
  private static async getAIHint(
    playerHand: number[],
    dealerUpCard: number,
    playerCalc: HandCalculation,
    runningCount?: number,
    trueCount?: number,
    canSplit?: boolean
  ): Promise<StrategyAdvice> {
    // Prepare input for AI strategy hint request
    const input: BlackjackStrategyHintInput = {
      playerHand,
      dealerUpCard,
      playerHasAce: playerCalc.isSoft,
      handTotal: playerCalc.total,
      canSplit,
      runningCount,
      trueCount
    };
    
    // Request hint from AI service
    const result = await blackjackStrategyHint(input);
    
    return {
      action: result.recommendedAction,
      advice: result.advice,
      confidence: result.confidence,
      isBasicStrategy: result.followsBasicStrategy === true,
      source: 'ai'
    };
  }
  
  /**
   * Get a rules-based strategy hint using basic strategy tables
   * 
   * @param playerCalc - Calculated properties of the player's hand
   * @param dealerUpCard - The dealer's face-up card value
   * @param canSplit - Whether the hand can be split
   * @returns Strategy advice based on basic strategy rules
   */
  private static getRulesBasedHint(
    playerCalc: HandCalculation,
    dealerUpCard: number,
    canSplit?: boolean
  ): StrategyAdvice {
    // Normalize dealer card (10/J/Q/K all count as 10)
    const dealerCard = dealerUpCard === 1 ? 11 : Math.min(dealerUpCard, 10);
    
    // Determine which strategy table to use
    let action: PlayerAction = 'stand';
    let tableType: 'hard' | 'soft' | 'pairs' = 'hard';
    
    if (canSplit) {
      // Handle pairs (two cards of same value)
      tableType = 'pairs';
      const pairValue = playerCalc.total / 2;
      action = this.lookupBasicStrategy(tableType, pairValue, dealerCard) as PlayerAction;
    } else if (playerCalc.isSoft) {
      // Handle soft hands (contains Ace counted as 11)
      tableType = 'soft';
      action = this.lookupBasicStrategy(tableType, playerCalc.total, dealerCard) as PlayerAction;
    } else {
      // Handle hard hands (no Ace as 11)
      tableType = 'hard';
      action = this.lookupBasicStrategy(tableType, playerCalc.total, dealerCard) as PlayerAction;
    }
    
    // If action is undefined, provide a safe fallback
    if (!action) {
      action = playerCalc.total < 17 ? 'hit' : 'stand';
    }
    
    return {
      action,
      advice: ACTION_EXPLANATIONS[action],
      isBasicStrategy: true,
      source: 'rules'
    };
  }
  
  /**
   * Lookup the correct action in basic strategy tables
   * 
   * @param tableType - Which table to use (hard, soft, pairs)
   * @param playerValue - The player's hand value to lookup
   * @param dealerCard - The dealer's card value to lookup
   * @returns The recommended action or undefined if not found
   */
  private static lookupBasicStrategy(
    tableType: 'hard' | 'soft' | 'pairs', 
    playerValue: number, 
    dealerCard: number
  ): string | undefined {
    // Get the appropriate strategy table
    const table = BASIC_STRATEGY[tableType];
    
    // Normalize player value for lookups
    let normalizedPlayerValue = playerValue;
    
    // Handle values outside the table ranges
    if (tableType === 'hard') {
      if (playerValue < 8) return 'hit';
      if (playerValue > 17) return 'stand';
      normalizedPlayerValue = Math.min(Math.max(playerValue, 8), 17);
    } else if (tableType === 'soft') {
      if (playerValue < 13) return 'hit';
      if (playerValue > 21) return 'stand';
      normalizedPlayerValue = Math.min(Math.max(playerValue, 13), 21);
    }
    
    // Lookup the action in the table
    if (table[normalizedPlayerValue as keyof typeof table]) {
      const actions = table[normalizedPlayerValue as keyof typeof table] as Record<number, string>;
      return actions[dealerCard];
    }
    
    // Fallback for any missing values
    return playerValue < 17 ? 'hit' : 'stand';
  }
}