/**
 * @fileOverview Defines the core Blackjack game logic, including deck management, player/dealer hands, and game actions.
 * Intended to encapsulate game state and rules, separating them from the UI.
 */

import { 
  GAME_CONSTANTS, 
  CARD_VALUES, 
  DEFAULT_GAME_SETTINGS, 
  PLAYER_ACTIONS, 
  GAME_STATES,
  COUNTING_SYSTEMS
} from '@/lib/constants';

import {
  Card,
  GameSettings,
  GameState,
  PlayerHand,
  DealerHand,
  GameStatus,
  GameResult,
  HandCalculation,
  ActionResult,
  GameInitParams,
  CountingSystemKey
} from '@/types/game';

/**
 * Represents a game of Blackjack.
 * Manages the deck, player hands, dealer hand, and game state.
 * Provides methods for game actions like hitting, standing, etc.
 */
export class BlackjackGame {
  /** The current deck of cards */
  private deck: Card[];
  /** Game configuration settings */
  private settings: GameSettings;
  /** Current game state */
  private state: GameState;
  /** Current running count */
  private runningCount: number = 0;

  /**
   * Initializes a new Blackjack game.
   * @param params - Optional settings to configure the game
   */
  constructor(params: GameInitParams = {}) {
    // Initialize settings with defaults and any overrides
    this.settings = {
      deckCount: params.settings?.deckCount ?? DEFAULT_GAME_SETTINGS.deckCount,
      dealerStandsOnSoft17: params.settings?.dealerStandsOnSoft17 ?? DEFAULT_GAME_SETTINGS.dealerStandsOnSoft17,
      doubleAfterSplit: params.settings?.doubleAfterSplit ?? DEFAULT_GAME_SETTINGS.doubleAfterSplit,
      surrenderAllowed: params.settings?.surrenderAllowed ?? DEFAULT_GAME_SETTINGS.surrenderAllowed,
      resplitAcesAllowed: params.settings?.resplitAcesAllowed ?? DEFAULT_GAME_SETTINGS.resplitAcesAllowed,
      maxSplits: params.settings?.maxSplits ?? DEFAULT_GAME_SETTINGS.maxSplits,
      blackjackPayout: params.settings?.blackjackPayout ?? DEFAULT_GAME_SETTINGS.blackjackPayout,
      countingSystem: params.settings?.countingSystem ?? DEFAULT_GAME_SETTINGS.countingSystem as CountingSystemKey,
      initialBankroll: params.initialBankroll ?? DEFAULT_GAME_SETTINGS.initialBankroll,
      minimumBet: params.settings?.minimumBet ?? DEFAULT_GAME_SETTINGS.minimumBet,
      maximumBet: params.settings?.maximumBet ?? DEFAULT_GAME_SETTINGS.maximumBet,
      gameMode: params.settings?.gameMode ?? DEFAULT_GAME_SETTINGS.gameMode,
      showCountInPractice: params.settings?.showCountInPractice ?? DEFAULT_GAME_SETTINGS.showCountInPractice,
      allowHintsInChallenge: params.settings?.allowHintsInChallenge ?? DEFAULT_GAME_SETTINGS.allowHintsInChallenge,
      countVerificationFrequency: params.settings?.countVerificationFrequency ?? DEFAULT_GAME_SETTINGS.countVerificationFrequency
    };
    
    // First create an initial deck
    this.deck = this.createInitialDeck(this.settings.deckCount);
    
    // Initialize game state before shuffling
    this.state = this.createInitialState();
    
    // Reset the running count when creating a new deck
    this.runningCount = 0;
    
    // Now shuffle the deck
    this.deck = this.shuffleDeckArray(this.deck);
    
    // Update the cards remaining count in the state
    this.updateCardsRemaining(this.deck.length);
  }

  /**
   * Creates the initial empty game state
   * @private
   */
  private createInitialState(): GameState {
    return {
      playerHands: [],
      activeHandIndex: 0,
      dealerHand: {
        cards: [],
        total: 0,
        isSoft: false,
        isRevealed: false,
        isBlackjack: false
      },
      gameStatus: 'idle',
      outcome: null,
      bankroll: this.settings.initialBankroll,
      cardsRemaining: 0, // Will be updated after deck is created
      count: 0,
      trueCount: 0,
      resultMessage: '',
      recommendedBet: this.settings.minimumBet
    };
  }

  /**
   * Creates an initial unshuffled deck of cards.
   * @param numberOfDecks - The number of decks to include.
   * @returns An array of strings representing the unshuffled deck.
   * @private
   */
  private createInitialDeck(numberOfDecks: number): Card[] {
    const deck: Card[] = [];
    for (let i = 0; i < numberOfDecks; i++) {
      for (const suit of GAME_CONSTANTS.SUITS) {
        for (const value of GAME_CONSTANTS.VALUES) {
          deck.push(`${suit}${value}`);
        }
      }
    }
    return deck;
  }

  /**
   * Creates a new shuffled deck for the game.
   * @param numberOfDecks - The number of decks to include.
   * @returns An array of strings representing the shuffled deck.
   * @private
   */
  private createNewDeck(numberOfDecks: number): Card[] {
    // Create unshuffled deck
    const deck = this.createInitialDeck(numberOfDecks);
    
    // Reset the running count when creating a new deck
    this.runningCount = 0;
    this.state.count = 0;
    this.state.trueCount = 0;
    
    // Return shuffled deck
    return this.shuffleDeckArray(deck);
  }

  /**
   * Shuffles an array of cards using the Fisher-Yates (Knuth) algorithm.
   * @param deck - The array of card strings to shuffle.
   * @returns A new array containing the shuffled cards.
   * @private
   */
  private shuffleDeckArray(deck: Card[]): Card[] {
    const shuffledDeck = [...deck];
    for (let i = shuffledDeck.length - 1; i > 0; i--) {
      const j = Math.floor(Math.random() * (i + 1));
      [shuffledDeck[i], shuffledDeck[j]] = [shuffledDeck[j], shuffledDeck[i]];
    }

    // Burn the first card as per casino procedure
    if (shuffledDeck.length > GAME_CONSTANTS.BURN_CARDS) {
      shuffledDeck.splice(0, GAME_CONSTANTS.BURN_CARDS);
    }
    
    return shuffledDeck;
  }

  /**
   * Updates the cards remaining and calculates the true count
   * @param cardsRemaining - Number of cards remaining in the deck
   * @private
   */
  private updateCardsRemaining(cardsRemaining: number): void {
    this.state.cardsRemaining = cardsRemaining;
    
    // Calculate decks remaining (roughly)
    const decksRemaining = cardsRemaining / 52;
    
    // Update true count (running count / decks remaining)
    if (decksRemaining > 0) {
      this.state.trueCount = Math.round((this.runningCount / decksRemaining) * 10) / 10;
    } else {
      this.state.trueCount = 0;
    }
    
    // Update count in state
    this.state.count = this.runningCount;
  }

  /**
   * Calculates the Blackjack value of a card string.
   * @param card - The card string (e.g., "SK", "DA").
   * @returns The numerical Blackjack value of the card.
   * @private
   */
  private getCardValue(card: Card): number {
    const value = card.substring(1);
    return CARD_VALUES[value as keyof typeof CARD_VALUES];
  }

  /**
   * Updates the count based on the card counting system
   * @param card - The card that was dealt
   * @private
   */
  private updateCount(card: Card): void {
    const value = card.substring(1);
    const countSystem = COUNTING_SYSTEMS[this.settings.countingSystem];
    
    if (countSystem && countSystem.values[value as keyof typeof countSystem.values] !== undefined) {
      this.runningCount += countSystem.values[value as keyof typeof countSystem.values];
      
      // Update count in game state
      if (this.state) {
        this.state.count = this.runningCount;
        // Update true count
        this.updateCardsRemaining(this.deck.length);
      }
    }
  }

  /**
   * Calculates the total value of a hand in Blackjack, accounting for Aces.
   * @param hand - An array of card strings representing the hand.
   * @returns An object containing the hand's `total` value and whether it's `isSoft`.
   * @private
   */
  private calculateHandTotal(cards: Card[]): HandCalculation {
    let total = 0;
    let aceCount = 0;

    for (const card of cards) {
      const value = this.getCardValue(card);
      if (value === 11) {
        aceCount++;
      }
      total += value;
    }

    // Adjust for aces if needed
    let isSoft = false;
    while (total > 21 && aceCount > 0) {
      total -= 10; // Convert an Ace from 11 to 1
      aceCount--;
    }

    // A hand is "soft" if it contains an Ace being counted as 11
    isSoft = aceCount > 0 && total <= 21;

    return { total, isSoft };
  }

  /**
   * Check if a hand is a blackjack (21 with exactly 2 cards)
   * @param cards - The cards in the hand
   * @returns Whether the hand is a blackjack
   * @private
   */
  private isBlackjack(cards: Card[]): boolean {
    return cards.length === 2 && this.calculateHandTotal(cards).total === 21;
  }

  /**
   * Start a new round with the specified bet amount
   * @param betAmount - The amount to bet on this hand
   * @returns The updated game state
   * @public
   */
  public placeBet(betAmount: number): ActionResult {
    // Validate bet amount
    if (betAmount < this.settings.minimumBet) {
      return {
        success: false,
        error: `Bet must be at least ${this.settings.minimumBet}`,
        gameState: this.getGameState()
      };
    }
    
    if (betAmount > this.settings.maximumBet) {
      return {
        success: false,
        error: `Bet cannot exceed ${this.settings.maximumBet}`,
        gameState: this.getGameState()
      };
    }
    
    if (betAmount > this.state.bankroll) {
      return {
        success: false,
        error: 'Bet cannot exceed available bankroll',
        gameState: this.getGameState()
      };
    }
    
    // Ensure we have enough cards for a new round
    if (this.deck.length < 15) { // Ensure we have enough cards for a full round with hits
      this.deck = this.createNewDeck(this.settings.deckCount);
    }
    
    // Update game state for a new round
    this.state.gameStatus = 'betting';
    this.state.outcome = null;
    this.state.resultMessage = '';
    this.state.dealerHand = {
      cards: [],
      total: 0,
      isSoft: false,
      isRevealed: false,
      isBlackjack: false
    };
    
    // Create initial player hand
    const initialHand: PlayerHand = {
      cards: [],
      total: 0,
      isSoft: false,
      bet: betAmount,
      isDone: false,
      isBusted: false,
      isBlackjack: false,
      canSplit: false,
      canDouble: false
    };
    
    this.state.playerHands = [initialHand];
    this.state.activeHandIndex = 0;
    
    // Deduct bet from bankroll
    this.state.bankroll -= betAmount;
    
    // Deal initial cards
    return this.dealInitialCards();
  }

  /**
   * Deal the initial cards for a round
   * @returns The result of the deal action
   * @private
   */
  private dealInitialCards(): ActionResult {
    // Deal in the standard blackjack order: player, dealer, player, dealer
    const playerHand = this.state.playerHands[0];
    
    // First card to player (face up)
    const playerCard1 = this.drawCard();
    if (!playerCard1) {
      return {
        success: false,
        error: 'Failed to draw card from deck',
        gameState: this.getGameState()
      };
    }
    playerHand.cards.push(playerCard1);
    
    // First card to dealer (face up)
    const dealerCard1 = this.drawCard();
    if (!dealerCard1) {
      return {
        success: false,
        error: 'Failed to draw card from deck',
        gameState: this.getGameState()
      };
    }
    this.state.dealerHand.cards.push(dealerCard1);
    
    // Second card to player (face up)
    const playerCard2 = this.drawCard();
    if (!playerCard2) {
      return {
        success: false,
        error: 'Failed to draw card from deck',
        gameState: this.getGameState()
      };
    }
    playerHand.cards.push(playerCard2);
    
    // Second card to dealer (face down)
    const dealerCard2 = this.drawCard();
    if (!dealerCard2) {
      return {
        success: false,
        error: 'Failed to draw card from deck',
        gameState: this.getGameState()
      };
    }
    this.state.dealerHand.cards.push(dealerCard2);
    
    // Update hand totals
    const playerHandCalc = this.calculateHandTotal(playerHand.cards);
    playerHand.total = playerHandCalc.total;
    playerHand.isSoft = playerHandCalc.isSoft;
    
    const dealerHandCalc = this.calculateHandTotal(this.state.dealerHand.cards);
    this.state.dealerHand.total = dealerHandCalc.total;
    this.state.dealerHand.isSoft = dealerHandCalc.isSoft;
    
    // Check for blackjacks
    playerHand.isBlackjack = this.isBlackjack(playerHand.cards);
    this.state.dealerHand.isBlackjack = this.isBlackjack(this.state.dealerHand.cards);
    
    // Handle immediate blackjacks
    if (playerHand.isBlackjack || this.state.dealerHand.isBlackjack) {
      return this.resolveBlackjacks();
    }
    
    // Update action possibilities
    playerHand.canDouble = true; // Can always double on first two cards
    playerHand.canSplit = this.getCardValue(playerCard1) === this.getCardValue(playerCard2);
    
    // Set the game status to playing
    this.state.gameStatus = 'playing';
    
    return {
      success: true,
      gameState: this.getGameState()
    };
  }

  /**
   * Resolve the outcome when either player or dealer has blackjack
   * @returns The result of the action
   * @private
   */
  private resolveBlackjacks(): ActionResult {
    const playerHand = this.state.playerHands[0];
    
    // Reveal dealer's hand
    this.state.dealerHand.isRevealed = true;
    
    if (playerHand.isBlackjack && this.state.dealerHand.isBlackjack) {
      // Both have blackjack - push
      this.state.outcome = 'tie';
      this.state.resultMessage = "It's a push! Both have Blackjack.";
      this.state.bankroll += playerHand.bet; // Return the bet
      playerHand.isDone = true;
    } else if (playerHand.isBlackjack) {
      // Player has blackjack, dealer doesn't
      this.state.outcome = 'player';
      this.state.resultMessage = 'Blackjack! You win!';
      this.state.bankroll += playerHand.bet + (playerHand.bet * this.settings.blackjackPayout);
      playerHand.isDone = true;
    } else if (this.state.dealerHand.isBlackjack) {
      // Dealer has blackjack, player doesn't
      this.state.outcome = 'dealer';
      this.state.resultMessage = 'Dealer Blackjack! Dealer wins.';
      playerHand.isDone = true;
    }
    
    this.state.gameStatus = 'ended';
    
    return {
      success: true,
      gameState: this.getGameState()
    };
  }

  /**
   * Draw a card from the deck
   * @returns The drawn card or null if the deck is empty
   * @private
   */
  private drawCard(): Card | null {
    if (this.deck.length === 0) {
      return null;
    }
    
    const card = this.deck.pop()!;
    
    // Update the count when a card is drawn
    this.updateCount(card);
    
    // Update cards remaining in the state
    this.updateCardsRemaining(this.deck.length);
    
    return card;
  }

  /**
   * Handles the 'Hit' action for the current active hand
   * @returns The result of the hit action
   * @public
   */
  public hit(): ActionResult {
    if (this.state.gameStatus !== 'playing') {
      return {
        success: false,
        error: 'Cannot hit - game is not in progress',
        gameState: this.getGameState()
      };
    }
    
    const activeHand = this.state.playerHands[this.state.activeHandIndex];
    
    if (activeHand.isDone) {
      return {
        success: false,
        error: 'This hand is already completed',
        gameState: this.getGameState()
      };
    }
    
    // Draw a card and add it to the active hand
    const card = this.drawCard();
    if (!card) {
      return {
        success: false,
        error: 'Failed to draw card from deck',
        gameState: this.getGameState()
      };
    }
    
    activeHand.cards.push(card);
    
    // Update hand total
    const handCalc = this.calculateHandTotal(activeHand.cards);
    activeHand.total = handCalc.total;
    activeHand.isSoft = handCalc.isSoft;
    
    // Check for bust
    if (activeHand.total > 21) {
      activeHand.isBusted = true;
      activeHand.isDone = true;
      
      // Can no longer double or split after busting
      activeHand.canDouble = false;
      activeHand.canSplit = false;
      
      // Move to next hand or dealer play
      return this.proceedToNextHandOrDealer();
    }
    
    // After hitting, can no longer double or split
    activeHand.canDouble = false;
    activeHand.canSplit = false;
    
    return {
      success: true,
      gameState: this.getGameState()
    };
  }

  /**
   * Handles the 'Stand' action for the current active hand
   * @returns The result of the stand action
   * @public
   */
  public stand(): ActionResult {
    if (this.state.gameStatus !== 'playing') {
      return {
        success: false,
        error: 'Cannot stand - game is not in progress',
        gameState: this.getGameState()
      };
    }
    
    const activeHand = this.state.playerHands[this.state.activeHandIndex];
    
    if (activeHand.isDone) {
      return {
        success: false,
        error: 'This hand is already completed',
        gameState: this.getGameState()
      };
    }
    
    // Mark the hand as done
    activeHand.isDone = true;
    
    // Move to next hand or dealer play
    return this.proceedToNextHandOrDealer();
  }

  /**
   * Handles the 'Double Down' action for the current active hand
   * @returns The result of the double down action
   * @public
   */
  public doubleDown(): ActionResult {
    if (this.state.gameStatus !== 'playing') {
      return {
        success: false,
        error: 'Cannot double down - game is not in progress',
        gameState: this.getGameState()
      };
    }
    
    const activeHand = this.state.playerHands[this.state.activeHandIndex];
    
    if (activeHand.isDone) {
      return {
        success: false,
        error: 'This hand is already completed',
        gameState: this.getGameState()
      };
    }
    
    if (!activeHand.canDouble) {
      return {
        success: false,
        error: 'Cannot double down on this hand',
        gameState: this.getGameState()
      };
    }
    
    // Check if player has enough bankroll
    if (activeHand.bet > this.state.bankroll) {
      return {
        success: false,
        error: 'Insufficient funds to double down',
        gameState: this.getGameState()
      };
    }
    
    // Deduct the additional bet from bankroll
    this.state.bankroll -= activeHand.bet;
    
    // Double the bet
    activeHand.bet *= 2;
    
    // Draw exactly one card
    const card = this.drawCard();
    if (!card) {
      return {
        success: false,
        error: 'Failed to draw card from deck',
        gameState: this.getGameState()
      };
    }
    
    activeHand.cards.push(card);
    
    // Update hand total
    const handCalc = this.calculateHandTotal(activeHand.cards);
    activeHand.total = handCalc.total;
    activeHand.isSoft = handCalc.isSoft;
    
    // Check for bust
    if (activeHand.total > 21) {
      activeHand.isBusted = true;
    }
    
    // Hand is done after doubling down
    activeHand.isDone = true;
    activeHand.canDouble = false;
    activeHand.canSplit = false;
    
    // Move to next hand or dealer play
    return this.proceedToNextHandOrDealer();
  }

  /**
   * Move to the next hand or dealer play based on game state
   * @returns The result of the action
   * @private
   */
  private proceedToNextHandOrDealer(): ActionResult {
    // If there are more hands to play, move to the next one
    if (this.state.activeHandIndex < this.state.playerHands.length - 1) {
      this.state.activeHandIndex++;
      return {
        success: true,
        gameState: this.getGameState()
      };
    }
    
    // All hands are done, move to dealer play
    return this.playDealer();
  }

  /**
   * Play the dealer's turn according to house rules
   * @returns The result of the dealer's play
   * @private
   */
  private playDealer(): ActionResult {
    // Reveal dealer's hand
    this.state.dealerHand.isRevealed = true;
    
    // Check if all player hands are busted
    const allHandsBusted = this.state.playerHands.every(hand => hand.isBusted);
    
    if (allHandsBusted) {
      // If all player hands are busted, dealer doesn't need to draw
      return this.determineGameOutcomes();
    }
    
    // Dealer plays according to fixed rules
    const dealerStandThreshold = this.settings.dealerStandsOnSoft17 ? 17 : 18;
    
    while (this.state.dealerHand.total < dealerStandThreshold || 
           (this.state.dealerHand.total === 17 && this.state.dealerHand.isSoft && !this.settings.dealerStandsOnSoft17)) {
      
      // Dealer draws a card
      const card = this.drawCard();
      if (!card) {
        return {
          success: false,
          error: 'Failed to draw card for dealer',
          gameState: this.getGameState()
        };
      }
      
      this.state.dealerHand.cards.push(card);
      
      // Update dealer hand total
      const dealerHandCalc = this.calculateHandTotal(this.state.dealerHand.cards);
      this.state.dealerHand.total = dealerHandCalc.total;
      this.state.dealerHand.isSoft = dealerHandCalc.isSoft;
    }
    
    // Determine game outcomes
    return this.determineGameOutcomes();
  }

  /**
   * Determine the outcomes for all player hands
   * @returns The result of the action
   * @private
   */
  private determineGameOutcomes(): ActionResult {
    // Game is over
    this.state.gameStatus = 'ended';
    
    // Check each player hand against the dealer
    let overallOutcome: 'player' | 'dealer' | 'tie' | null = null;
    let resultMessage = '';
    let netWinnings = 0;
    
    // Check if dealer busted
    const dealerBusted = this.state.dealerHand.total > 21;
    
    for (const hand of this.state.playerHands) {
      if (hand.isBusted) {
        // Player busted, dealer wins this hand
        // (bet already deducted from bankroll)
      } else if (dealerBusted) {
        // Dealer busted, player wins this hand
        this.state.bankroll += hand.bet * 2; // Original bet + winnings
        netWinnings += hand.bet;
      } else if (hand.total > this.state.dealerHand.total) {
        // Player has higher total, player wins
        this.state.bankroll += hand.bet * 2; // Original bet + winnings
        netWinnings += hand.bet;
      } else if (hand.total < this.state.dealerHand.total) {
        // Dealer has higher total, dealer wins
        // (bet already deducted from bankroll)
        netWinnings -= hand.bet;
      } else {
        // Equal totals, it's a push
        this.state.bankroll += hand.bet; // Return the original bet
      }
    }
    
    // Set overall outcome and message
    if (netWinnings > 0) {
      overallOutcome = 'player';
      if (dealerBusted) {
        resultMessage = 'Dealer busted! You win!';
      } else {
        resultMessage = 'You win!';
      }
    } else if (netWinnings < 0) {
      overallOutcome = 'dealer';
      if (this.state.playerHands.every(hand => hand.isBusted)) {
        resultMessage = 'You busted! Dealer wins.';
      } else {
        resultMessage = 'Dealer wins!';
      }
    } else {
      overallOutcome = 'tie';
      resultMessage = "It's a push!";
    }
    
    this.state.outcome = overallOutcome;
    this.state.resultMessage = resultMessage;
    
    return {
      success: true,
      gameState: this.getGameState()
    };
  }

  /**
   * Get a copy of the current game state
   * @returns The current game state
   * @public
   */
  public getGameState(): GameState {
    // Return a deep copy to prevent unwanted modifications
    return JSON.parse(JSON.stringify(this.state));
  }

  /**
   * Get detailed information about the game result
   * @returns Game result information
   * @public
   */
  public getGameResult(): GameResult {
    if (this.state.gameStatus !== 'ended') {
      return {
        result: 'Game is still in progress',
        winnings: 0
      };
    }
    
    // Calculate winnings
    let winnings = 0;
    const initialBet = this.state.playerHands[0].bet;
    
    if (this.state.outcome === 'player') {
      if (this.state.playerHands[0].isBlackjack) {
        winnings = initialBet * this.settings.blackjackPayout;
      } else {
        winnings = initialBet;
      }
    } else if (this.state.outcome === 'dealer') {
      winnings = -initialBet;
    }
    
    return {
      result: this.state.resultMessage,
      winnings
    };
  }

  /**
   * Handle the 'Split' action for the current active hand
   * @returns The result of the split action
   * @public
   */
  public split(): ActionResult {
    if (this.state.gameStatus !== 'playing') {
      return {
        success: false,
        error: 'Cannot split - game is not in progress',
        gameState: this.getGameState()
      };
    }
    
    const activeHand = this.state.playerHands[this.state.activeHandIndex];
    
    if (activeHand.isDone) {
      return {
        success: false,
        error: 'This hand is already completed',
        gameState: this.getGameState()
      };
    }
    
    if (!activeHand.canSplit) {
      return {
        success: false,
        error: 'Cannot split this hand',
        gameState: this.getGameState()
      };
    }
    
    // Check if there are already maximum splits
    if (this.state.playerHands.length >= this.settings.maxSplits + 1) {
      return {
        success: false,
        error: `Cannot split more than ${this.settings.maxSplits} times`,
        gameState: this.getGameState()
      };
    }
    
    // Check if player has enough bankroll for another bet
    if (activeHand.bet > this.state.bankroll) {
      return {
        success: false,
        error: 'Insufficient funds to split',
        gameState: this.getGameState()
      };
    }
    
    // Create two new hands from the split
    const card1 = activeHand.cards[0];
    const card2 = activeHand.cards[1];
    
    // Special handling for split aces
    const isAceSplit = card1.includes('A');
    
    // First hand (modify existing hand)
    activeHand.cards = [card1];
    
    // Draw a card for the first hand
    const newCard1 = this.drawCard();
    if (!newCard1) {
      return {
        success: false,
        error: 'Failed to draw card from deck',
        gameState: this.getGameState()
      };
    }
    activeHand.cards.push(newCard1);
    
    // Update first hand total
    const handCalc1 = this.calculateHandTotal(activeHand.cards);
    activeHand.total = handCalc1.total;
    activeHand.isSoft = handCalc1.isSoft;
    
    // If splitting aces and re-splitting aces is not allowed, mark hand as done
    if (isAceSplit && !this.settings.resplitAcesAllowed) {
      activeHand.isDone = true;
      activeHand.canSplit = false;
      activeHand.canDouble = this.settings.doubleAfterSplit;
    } else {
      // Check if this hand can be split again
      activeHand.canSplit = this.getCardValue(card1) === this.getCardValue(newCard1) && 
                           this.state.playerHands.length < this.settings.maxSplits + 1;
      activeHand.canDouble = this.settings.doubleAfterSplit;
    }
    
    // Second hand (create new hand)
    const newHand: PlayerHand = {
      cards: [card2],
      total: 0,
      isSoft: false,
      bet: activeHand.bet,
      isDone: false,
      isBusted: false,
      isBlackjack: false,
      canSplit: false,
      canDouble: this.settings.doubleAfterSplit
    };
    
    // Deduct bet for the new hand
    this.state.bankroll -= activeHand.bet;
    
    // Draw a card for the second hand
    const newCard2 = this.drawCard();
    if (!newCard2) {
      return {
        success: false,
        error: 'Failed to draw card from deck',
        gameState: this.getGameState()
      };
    }
    newHand.cards.push(newCard2);
    
    // Update second hand total
    const handCalc2 = this.calculateHandTotal(newHand.cards);
    newHand.total = handCalc2.total;
    newHand.isSoft = handCalc2.isSoft;
    
    // If splitting aces and re-splitting aces is not allowed, mark hand as done
    if (isAceSplit && !this.settings.resplitAcesAllowed) {
      newHand.isDone = true;
    } else {
      // Check if second hand can be split
      newHand.canSplit = this.getCardValue(card2) === this.getCardValue(newCard2) && 
                        this.state.playerHands.length < this.settings.maxSplits + 1;
    }
    
    // Insert the new hand after the current hand
    this.state.playerHands.splice(this.state.activeHandIndex + 1, 0, newHand);
    
    // If the first hand is done (aces split), move to the next hand
    if (activeHand.isDone) {
      return this.proceedToNextHandOrDealer();
    }
    
    return {
      success: true,
      gameState: this.getGameState()
    };
  }

  /**
   * Get the current recommended bet based on true count
   * @returns The recommended bet amount
   * @public
   */
  public getRecommendedBet(): number {
    const trueCount = this.state.trueCount;
    const minBet = this.settings.minimumBet;
    const maxBet = Math.min(this.settings.maximumBet, this.state.bankroll);
    
    // Simple betting strategy based on true count
    if (trueCount <= 1) {
      return minBet;
    } else if (trueCount <= 2) {
      return Math.min(minBet * 2, maxBet);
    } else if (trueCount <= 3) {
      return Math.min(minBet * 4, maxBet);
    } else if (trueCount <= 4) {
      return Math.min(minBet * 8, maxBet);
    } else {
      return Math.min(minBet * 12, maxBet);
    }
  }

  /**
   * Check if the player needs to shuffle the deck
   * @returns Whether a shuffle is needed
   * @public
   */
  public shouldShuffle(): boolean {
    // Shuffle when less than 25% of the cards remain
    const totalCards = this.settings.deckCount * 52;
    const penetration = GAME_CONSTANTS.DEFAULT_PENETRATION;
    
    return this.deck.length < totalCards * (1 - penetration);
  }

  /**
   * Manually shuffle the deck
   * @returns Success status
   * @public
   */
  public shuffleDeck(): boolean {
    this.deck = this.createNewDeck(this.settings.deckCount);
    this.updateCardsRemaining(this.deck.length);
    return true;
  }

  /**
   * Get the card count value for Hi-Lo system
   * @param card - The card to get the count value for
   * @returns The count value (+1, 0, or -1)
   * @public
   */
  public getCardCountValue(card: Card): number {
    const value = card.slice(1); // Remove suit, keep value
    const countingSystem = COUNTING_SYSTEMS[this.settings.countingSystem];
    
    return countingSystem.values[value as keyof typeof countingSystem.values] || 0;
  }

  /**
   * Get the current running count
   * @returns The running count value
   * @public
   */
  public getRunningCount(): number {
    return this.runningCount;
  }

  /**
   * Get the current true count
   * @returns The true count value
   * @public
   */
  public getTrueCount(): number {
    return this.state.trueCount;
  }

  /**
   * Get the number of remaining decks
   * @returns The number of decks remaining in the shoe
   * @public
   */
  public getRemainingDecks(): number {
    return this.deck.length / 52;
  }

  /**
   * Get visible game state based on current mode
   * @returns Filtered game state appropriate for the current mode
   * @public
   */
  public getVisibleState(): GameState {
    const state = this.getGameState();
    
    // In challenge mode, hide count information unless specifically allowed
    if (this.settings.gameMode === 'challenge') {
      return {
        ...state,
        count: this.settings.showCountInPractice ? state.count : 0,
        trueCount: this.settings.showCountInPractice ? state.trueCount : 0,
      };
    }
    
    // In practice mode, show all information
    return state;
  }

  /**
   * Update game mode settings
   * @param newSettings - Partial settings to update
   * @public
   */
  public updateGameMode(newSettings: Partial<GameSettings>): void {
    this.settings = { ...this.settings, ...newSettings };
  }
}