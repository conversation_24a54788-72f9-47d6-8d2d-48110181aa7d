/**
 * Game constants for the Stack Advantage Blackjack Trainer
 * Contains card values, deck configuration, game rules, and UI breakpoints
 */

/**
 * Basic card and deck configuration constants
 */
export const GAME_CONSTANTS = {
  /** Card suits represented by their first letter */
  SUITS: ['C', 'D', 'H', 'S'],
  /** Card values from 2-10 and face cards */
  VALUES: ['2', '3', '4', '5', '6', '7', '8', '9', '10', 'J', 'Q', 'K', 'A'],
  /** Default number of decks used in the shoe */
  DEFAULT_DECKS: 2,
  /** Dealer must stand on soft 17 by default */
  DEALER_STANDS_ON_SOFT_17: true,
  /** Default penetration (percentage of cards dealt before reshuffling) */
  DEFAULT_PENETRATION: 0.75,
  /** Number of cards to burn after shuffling */
  BURN_CARDS: 1,
} as const

/**
 * Card numerical values for blackjack scoring
 */
export const CARD_VALUES = {
  '2': 2,
  '3': 3,
  '4': 4,
  '5': 5,
  '6': 6,
  '7': 7,
  '8': 8,
  '9': 9,
  '10': 10,
  'J': 10,
  'Q': 10,
  'K': 10,
  'A': 11, // Aces are worth 11 or 1, handled in game logic
} as const

/**
 * Common counting systems and their values
 */
export const COUNTING_SYSTEMS = {
  HI_LO: {
    name: 'Hi-Lo',
    values: {
      '2': 1,
      '3': 1,
      '4': 1,
      '5': 1,
      '6': 1,
      '7': 0,
      '8': 0,
      '9': 0,
      '10': -1,
      'J': -1,
      'Q': -1,
      'K': -1,
      'A': -1,
    },
  },
  HI_OPT_I: {
    name: 'Hi-Opt I',
    values: {
      '2': 0,
      '3': 1,
      '4': 1,
      '5': 1,
      '6': 1,
      '7': 0,
      '8': 0,
      '9': 0,
      '10': -1,
      'J': -1,
      'Q': -1,
      'K': -1,
      'A': 0,
    },
  },
  HI_OPT_II: {
    name: 'Hi-Opt II',
    values: {
      '2': 1,
      '3': 1,
      '4': 2,
      '5': 2,
      '6': 1,
      '7': 1,
      '8': 0,
      '9': 0,
      '10': -2,
      'J': -2,
      'Q': -2,
      'K': -2,
      'A': 0,
    },
  },
  KO: {
    name: 'Knockout (KO)',
    values: {
      '2': 1,
      '3': 1,
      '4': 1,
      '5': 1,
      '6': 1,
      '7': 1,
      '8': 0,
      '9': 0,
      '10': -1,
      'J': -1,
      'Q': -1,
      'K': -1,
      'A': -1,
    },
  },
  OMEGA_II: {
    name: 'Omega II',
    values: {
      '2': 1,
      '3': 1,
      '4': 2,
      '5': 2,
      '6': 2,
      '7': 1,
      '8': 0,
      '9': -1,
      '10': -2,
      'J': -2,
      'Q': -2,
      'K': -2,
      'A': 0,
    },
  },
} as const

/**
 * Training mode constants
 */
export const GAME_MODES = {
  PRACTICE: 'practice',
  CHALLENGE: 'challenge',
} as const

/**
 * Default game settings
 */
export const DEFAULT_GAME_SETTINGS = {
  countingSystem: 'HI_LO',
  deckCount: 2,
  dealerStandsOnSoft17: true,
  doubleAfterSplit: true,
  surrenderAllowed: true,
  resplitAcesAllowed: false,
  maxSplits: 3,
  blackjackPayout: 1.5, // 3:2 payout for blackjack
  initialBankroll: 1000,
  minimumBet: 5,
  maximumBet: 500,
  gameMode: GAME_MODES.PRACTICE,
  showCountInPractice: true,
  allowHintsInChallenge: false,
  countVerificationFrequency: 'round',
} as const

/**
 * Basic strategy mappings will be used to check correct plays
 * These will be more extensively defined in a separate file
 */
export const BASIC_STRATEGY = {
  HARD_HANDS: 'HARD_HANDS',
  SOFT_HANDS: 'SOFT_HANDS',
  PAIRS: 'PAIRS',
} as const

/**
 * Game actions available to the player
 */
export const PLAYER_ACTIONS = {
  HIT: 'hit',
  STAND: 'stand',
  DOUBLE: 'double',
  SPLIT: 'split',
  SURRENDER: 'surrender',
} as const

/**
 * Game states
 */
export const GAME_STATES = {
  IDLE: 'idle',
  BETTING: 'betting',
  DEALING: 'dealing',
  PLAYER_TURN: 'playerTurn',
  DEALER_TURN: 'dealerTurn',
  SHOWDOWN: 'showdown',
  FINISHED: 'finished',
} as const

/**
 * UI breakpoints for responsive design
 */
export const BREAKPOINTS = {
  MOBILE: 768,
  TABLET: 1024,
  DESKTOP: 1280,
} as const

/**
 * Animation durations in milliseconds
 */
export const ANIMATIONS = {
  CARD_DEAL: 300,
  CHIP_MOVEMENT: 500,
  RESULT_DISPLAY: 1500,
} as const

/**
 * Default theme configuration
 */
export const THEME = {
  TABLE_COLOR: 'bg-emerald-800',
  CARD_BACK_COLOR: 'bg-blue-700',
  CHIP_COLORS: {
    5: 'bg-red-600',
    25: 'bg-green-600',
    100: 'bg-black',
    500: 'bg-purple-800',
  },
} as const