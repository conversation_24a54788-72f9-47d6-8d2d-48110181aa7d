/**
 * @fileOverview Utility functions for the application.
 */
import { clsx, type ClassValue } from "clsx"
import { twMerge } from "tailwind-merge"

/**
 * Combines multiple class names or class value arrays into a single string,
 * resolving Tailwind CSS class conflicts intelligently.
 *
 * Uses `clsx` for flexible class name joining and `tailwind-merge` for conflict resolution.
 *
 * @param inputs - A list of class values (strings, arrays, objects).
 * @returns A merged string of class names.
 */
export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}
