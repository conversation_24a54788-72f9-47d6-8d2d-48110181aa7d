import { useState, useEffect, useCallback } from 'react'
import { BlackjackGame } from '@/lib/game/blackjack'
import { DEFAULT_GAME_SETTINGS, GAME_MODES } from '@/lib/constants'
import type { 
  GameState, 
  ActionResult,
  GameInitParams,
  GameMode,
  CountVerificationResult
} from '@/types/game'

/**
 * Custom hook for managing Blackjack game state and actions
 * 
 * @param initParams - Optional initial parameters for configuring the game
 * @returns An object containing game state and methods to control the game
 */
export function useBlackjack(initParams?: GameInitParams) {
  // Initialize game instance
  const [game] = useState(() => new BlackjackGame(initParams))
  
  // Game state
  const [gameState, setGameState] = useState<GameState>(game.getGameState())
  
  // Training mode state
  const [gameMode, setGameModeState] = useState<GameMode>(
    initParams?.settings?.gameMode ?? DEFAULT_GAME_SETTINGS.gameMode
  )
  
  // UI state for betting
  const [betAmount, setBetAmount] = useState<number>(DEFAULT_GAME_SETTINGS.minimumBet)
  const [betError, setBetError] = useState<string>("")
  
  /**
   * Updates the current bet amount with validation
   * @param amount - The new bet amount to set
   */
  const updateBetAmount = useCallback((amount: number) => {
    const bankroll = gameState.bankroll || DEFAULT_GAME_SETTINGS.initialBankroll
    
    if (amount > bankroll) {
      setBetError(`Cannot bet more than available bankroll ($${bankroll})`)
      setBetAmount(bankroll)
    } else if (amount < DEFAULT_GAME_SETTINGS.minimumBet) {
      setBetError(`Minimum bet is $${DEFAULT_GAME_SETTINGS.minimumBet}`)
      setBetAmount(DEFAULT_GAME_SETTINGS.minimumBet)
    } else if (amount > DEFAULT_GAME_SETTINGS.maximumBet) {
      setBetError(`Maximum bet is $${DEFAULT_GAME_SETTINGS.maximumBet}`)
      setBetAmount(DEFAULT_GAME_SETTINGS.maximumBet) 
    } else {
      setBetError("")
      setBetAmount(amount)
    }
  }, [gameState.bankroll])
  
  /**
   * Places a bet and deals cards to start a new round
   * @returns Result of the bet and deal action
   */
  const placeBet = useCallback((): ActionResult => {
    if (betError) {
      return {
        success: false,
        error: betError,
        gameState: gameState
      }
    }
    
    const result = game.placeBet(betAmount)
    
    if (result.success) {
      setGameState(result.gameState)
    }
    
    return result
  }, [game, betAmount, betError, gameState])
  
  /**
   * Performs a hit action in the game
   * @returns Result of the hit action
   */
  const hit = useCallback((): ActionResult => {
    const result = game.hit()
    
    if (result.success) {
      setGameState(result.gameState)
    }
    
    return result
  }, [game])
  
  /**
   * Performs a stand action in the game
   * @returns Result of the stand action
   */
  const stand = useCallback((): ActionResult => {
    const result = game.stand()
    
    if (result.success) {
      setGameState(result.gameState)
    }
    
    return result
  }, [game])
  
  /**
   * Performs a double down action in the game
   * @returns Result of the double down action
   */
  const doubleDown = useCallback((): ActionResult => {
    const result = game.doubleDown()
    
    if (result.success) {
      setGameState(result.gameState)
    }
    
    return result
  }, [game])
  
  /**
   * Performs a split action in the game
   * @returns Result of the split action
   */
  const split = useCallback((): ActionResult => {
    const result = game.split()
    
    if (result.success) {
      setGameState(result.gameState)
    }
    
    return result
  }, [game])
  
  /**
   * Resets the game to the betting phase
   * @returns The updated game state
   */
  const startNewRound = useCallback((): GameState => {
    // Reset UI state
    setBetAmount(DEFAULT_GAME_SETTINGS.minimumBet)
    setBetError("")
    
    // Create a temporary state for the UI while the game resets
    const updatedState = { ...gameState }
    updatedState.gameStatus = 'betting'
    updatedState.outcome = null
    updatedState.resultMessage = ''
    updatedState.playerHands = []
    updatedState.dealerHand = {
      cards: [],
      total: 0,
      isSoft: false,
      isRevealed: false,
      isBlackjack: false
    }
    
    setGameState(updatedState)
    return updatedState
  }, [gameState])
  
  /**
   * Sets the game mode and updates game settings
   * @param mode - The new game mode to set
   */
  const setGameMode = useCallback((mode: GameMode) => {
    setGameModeState(mode)
    game.updateGameMode({ 
      gameMode: mode,
      showCountInPractice: mode === GAME_MODES.PRACTICE,
      allowHintsInChallenge: mode === GAME_MODES.CHALLENGE ? false : true
    })
    // Update the game state to reflect new mode
    setGameState(game.getVisibleState())
  }, [game])

  /**
   * Computed property to determine if count should be visible
   */
  const isCountVisible = useCallback((): boolean => {
    if (gameMode === GAME_MODES.PRACTICE) {
      return true
    }
    return false // Hidden in challenge mode
  }, [gameMode])

  /**
   * Verifies the player's count against the actual count
   * @param playerCount - The count entered by the player
   * @returns Count verification result
   */
  const verifyCount = useCallback((playerCount: number): CountVerificationResult => {
    const actualCount = game.getRunningCount()
    const difference = Math.abs(playerCount - actualCount)
    const accuracy = Math.max(0, 100 - (difference * 10)) // 10% penalty per point difference
    const isCorrect = difference === 0

    return {
      playerCount,
      actualCount,
      accuracy,
      isCorrect
    }
  }, [game])

  // Update game state when mode changes
  useEffect(() => {
    setGameState(game.getVisibleState())
  }, [game, gameMode])
  
  // Expose the methods and state needed to control the game
  return {
    // Game state
    gameState,
    
    // Betting state
    betAmount,
    betError,
    updateBetAmount,
    
    // Game actions
    placeBet,
    hit,
    stand,
    doubleDown,
    split,
    startNewRound,
    
    // Game info
    getRecommendedBet: game.getRecommendedBet.bind(game),
    shouldShuffle: game.shouldShuffle.bind(game),
    shuffleDeck: game.shuffleDeck.bind(game),
    
    // Training mode
    gameMode,
    setGameMode,
    isCountVisible,
    verifyCount
  }
}