import { useState, useCallback } from 'react'

interface DecisionMetrics {
  correctDecisions: number
  totalDecisions: number
  accuracyPercentage: number
}

interface HandResult {
  outcome: 'win' | 'lose' | 'push' | 'blackjack'
  amount: number
}

interface SessionMetrics {
  handsPlayed: number
  wins: number
  losses: number
  pushes: number
  blackjacks: number
  netProfit: number
  winRate: number
}

interface UsePerformanceMetricsReturn {
  decisions: DecisionMetrics
  session: SessionMetrics
  recordDecision: (isCorrect: boolean) => void
  recordHandResult: (result: HandResult) => void
  resetSessionMetrics: () => void
}

/**
 * Custom hook for tracking player performance metrics in Blackjack
 * 
 * Keeps track of:
 * - Decision accuracy (correct vs. incorrect decisions)
 * - Game session statistics (hands played, win/loss record, profit)
 * 
 * @returns Object containing metrics and methods to update them
 */
export function usePerformanceMetrics(): UsePerformanceMetricsReturn {
  // Decision-making metrics (strategy accuracy)
  const [correctDecisions, setCorrectDecisions] = useState<number>(0)
  const [totalDecisions, setTotalDecisions] = useState<number>(0)
  
  // Session performance metrics
  const [handsPlayed, setHandsPlayed] = useState<number>(0)
  const [wins, setWins] = useState<number>(0)
  const [losses, setLosses] = useState<number>(0)
  const [pushes, setPushes] = useState<number>(0)
  const [blackjacks, setBlackjacks] = useState<number>(0)
  const [netProfit, setNetProfit] = useState<number>(0)
  
  /**
   * Records a player decision and whether it was correct according to basic strategy
   * @param isCorrect - Whether the decision matched optimal strategy
   */
  const recordDecision = useCallback((isCorrect: boolean) => {
    setTotalDecisions(prev => prev + 1)
    
    if (isCorrect) {
      setCorrectDecisions(prev => prev + 1)
    }
  }, [])
  
  /**
   * Records the result of a completed hand
   * @param result - The outcome and financial result of the hand
   */
  const recordHandResult = useCallback((result: HandResult) => {
    setHandsPlayed(prev => prev + 1)
    setNetProfit(prev => prev + result.amount)
    
    switch (result.outcome) {
      case 'win':
        setWins(prev => prev + 1)
        break
      case 'lose':
        setLosses(prev => prev + 1)
        break
      case 'push':
        setPushes(prev => prev + 1)
        break
      case 'blackjack':
        setBlackjacks(prev => prev + 1)
        setWins(prev => prev + 1) // Blackjack is also a win
        break
    }
  }, [])
  
  /**
   * Resets all session metrics to zero
   */
  const resetSessionMetrics = useCallback(() => {
    setHandsPlayed(0)
    setWins(0)
    setLosses(0)
    setPushes(0)
    setBlackjacks(0)
    setNetProfit(0)
    // Note: We don't reset decision metrics as those can persist across sessions
  }, [])
  
  // Calculate derived metrics
  const accuracyPercentage = totalDecisions > 0 
    ? (correctDecisions / totalDecisions) * 100 
    : 0
    
  const winRate = handsPlayed > 0 
    ? (wins / handsPlayed) * 100 
    : 0
  
  return {
    decisions: {
      correctDecisions,
      totalDecisions,
      accuracyPercentage
    },
    session: {
      handsPlayed,
      wins,
      losses,
      pushes,
      blackjacks,
      netProfit,
      winRate
    },
    recordDecision,
    recordHandResult,
    resetSessionMetrics
  }
}