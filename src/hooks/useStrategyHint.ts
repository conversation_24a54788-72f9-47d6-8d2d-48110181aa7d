/**
 * @fileOverview Custom hook for managing Blackjack strategy hints from AI
 * 
 * This hook provides a React interface for requesting, loading, and displaying
 * AI-powered strategy recommendations during Blackjack gameplay.
 */

import { useState, useCallback } from 'react'
import { blackjackStrategyHint, type BlackjackStrategyHintOutput } from '@/ai/flows/blackjack-strategy-hint'
import { StrategyAdvisor } from '@/lib/ai/strategy'
import type { HandCalculation, PlayerAction, GameSettings } from '@/types/game'

/**
 * Input parameters for requesting a strategy hint
 */
interface StrategyHintParams {
  /** Array of card values in the player's hand */
  playerHand: number[];
  /** The dealer's face-up card value */
  dealerUpCard: number;
  /** Calculated properties of the player's hand */
  playerCalc: HandCalculation;
  /** Optional game settings that might affect strategy */
  gameSettings?: Partial<GameSettings>;
  /** Optional current running count for card counting */
  runningCount?: number;
  /** Optional true count (running count / decks remaining) */
  trueCount?: number;
}

/**
 * Full strategy hint result with all details
 */
interface StrategyHintResult extends Omit<BlackjackStrategyHintOutput, 'recommendedAction'> {
  /** The recommended action to take */
  action: PlayerAction;
  /** When the hint was generated (for caching/expiry) */
  timestamp: number;
}

/**
 * Return value for the useStrategyHint hook
 */
interface UseStrategyHintReturn {
  /** The current strategy hint result if available */
  hint: StrategyHintResult | null;
  /** Whether a hint request is in progress */
  isLoading: boolean;
  /** Error message if hint request failed */
  error: string | null;
  /** Request a new strategy hint based on current game state */
  getHint: (params: StrategyHintParams) => Promise<StrategyHintResult | null>;
  /** Clear the current hint */
  clearHint: () => void;
}

/**
 * Custom hook for managing Blackjack strategy hints from AI
 * 
 * This hook handles requesting, loading and displaying Blackjack strategy hints
 * based on the current game state, with error handling and caching.
 * 
 * @returns Object containing the hint result, loading state, and control methods
 */
export function useStrategyHint(): UseStrategyHintReturn {
  const [hint, setHint] = useState<StrategyHintResult | null>(null)
  const [isLoading, setIsLoading] = useState<boolean>(false)
  const [error, setError] = useState<string | null>(null)
  
  /**
   * Requests a strategy hint from the AI based on the current game state
   * @param params - Game state parameters for generating the hint
   * @returns Promise resolving to the strategy hint result or null on error
   */
  const getHint = useCallback(async (params: StrategyHintParams): Promise<StrategyHintResult | null> => {
    setIsLoading(true)
    setError(null)
    
    try {
      // Use the StrategyAdvisor class to get the hint with fallback support
      const result = await StrategyAdvisor.getHint(
        params.playerHand,
        params.dealerUpCard,
        params.playerCalc,
        params.gameSettings
      )
      
      // Create the full hint result with timestamp
      const hintResult: StrategyHintResult = {
        action: result.action,
        advice: result.advice,
        reason: result.advice, // Use advice as reason if no separate reason provided
        confidence: result.confidence || 0.8,
        followsBasicStrategy: result.isBasicStrategy,
        timestamp: Date.now()
      }
      
      // Store the hint in state
      setHint(hintResult)
      return hintResult
      
    } catch (error) {
      console.error('Failed to get strategy hint:', error)
      const errorMessage = error instanceof Error ? error.message : 'Unknown error getting hint'
      setError(errorMessage)
      return null
      
    } finally {
      setIsLoading(false)
    }
  }, [])
  
  /**
   * Clears the current strategy hint and error state
   */
  const clearHint = useCallback(() => {
    setHint(null)
    setError(null)
    setIsLoading(false)
  }, [])
  
  return {
    hint,
    isLoading,
    error,
    getHint,
    clearHint
  }
}