/**
 * @fileOverview Custom React hook to determine if the current viewport width corresponds to a mobile device.
 */
import * as React from "react"

/**
 * Breakpoint width in pixels below which the viewport is considered mobile.
 */
const MOBILE_BREAKPOINT = 768

/**
 * Custom React hook that returns true if the window width is less than the mobile breakpoint.
 * Listens for window resize events to update the state.
 * @returns `true` if the viewport is considered mobile, `false` otherwise. Returns `undefined` during server-side rendering or before the first client-side check.
 */
export function useIsMobile() {
  const [isMobile, setIsMobile] = React.useState<boolean | undefined>(undefined)

  React.useEffect(() => {
    const mql = window.matchMedia(`(max-width: ${MOBILE_BREAKPOINT - 1}px)`)
    const onChange = () => {
      setIsMobile(window.innerWidth < MOBILE_BREAKPOINT)
    }
    mql.addEventListener("change", onChange)
    setIsMobile(window.innerWidth < MOBILE_BREAKPOINT)
    return () => mql.removeEventListener("change", onChange)
  }, [])

  return !!isMobile
}
