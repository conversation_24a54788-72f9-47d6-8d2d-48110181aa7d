import { useState, useEffect, useCallback } from 'react'
import { DEFAULT_GAME_SETTINGS } from '@/lib/constants'
import type { GameSettings, CountingSystemKey } from '@/types/game'

const STORAGE_KEY = 'stackjack_settings'

/**
 * Custom hook for managing persistent game settings
 * 
 * Handles saving and loading game settings to/from localStorage
 * with fallback to default settings.
 * 
 * @returns Object containing settings and methods to update them
 */
export function useGameSettings() {
  // Initialize with default settings, then load from storage if available
  const [settings, setSettings] = useState<GameSettings>(DEFAULT_GAME_SETTINGS)
  
  // Load settings from localStorage on initial mount
  useEffect(() => {
    try {
      const storedSettings = localStorage.getItem(STORAGE_KEY)
      
      if (storedSettings) {
        const parsedSettings = JSON.parse(storedSettings) as Partial<GameSettings>
        
        // Merge stored settings with defaults to handle missing properties
        // in case the settings structure was updated
        setSettings(prevSettings => ({
          ...prevSettings,
          ...parsedSettings
        }))
      }
    } catch (error) {
      console.error('Failed to load settings from localStorage:', error)
      // Fall back to default settings if there's an error
    }
  }, [])
  
  /**
   * Updates a single setting and persists the change to localStorage
   * @param key - The setting key to update
   * @param value - The new value for the setting
   */
  const updateSetting = useCallback(<K extends keyof GameSettings>(
    key: K, 
    value: GameSettings[K]
  ) => {
    setSettings(prevSettings => {
      const newSettings = {
        ...prevSettings,
        [key]: value
      }
      
      // Persist to localStorage
      try {
        localStorage.setItem(STORAGE_KEY, JSON.stringify(newSettings))
      } catch (error) {
        console.error('Failed to save settings to localStorage:', error)
      }
      
      return newSettings
    })
  }, [])
  
  /**
   * Updates multiple settings at once and persists to localStorage
   * @param newSettings - Partial settings object to merge with current settings
   */
  const updateSettings = useCallback((newSettings: Partial<GameSettings>) => {
    setSettings(prevSettings => {
      const updatedSettings = {
        ...prevSettings,
        ...newSettings
      }
      
      // Persist to localStorage
      try {
        localStorage.setItem(STORAGE_KEY, JSON.stringify(updatedSettings))
      } catch (error) {
        console.error('Failed to save settings to localStorage:', error)
      }
      
      return updatedSettings
    })
  }, [])
  
  /**
   * Resets all settings to default values
   */
  const resetSettings = useCallback(() => {
    setSettings(DEFAULT_GAME_SETTINGS)
    
    // Remove from localStorage
    try {
      localStorage.removeItem(STORAGE_KEY)
    } catch (error) {
      console.error('Failed to remove settings from localStorage:', error)
    }
  }, [])
  
  return {
    settings,
    updateSetting,
    updateSettings,
    resetSettings
  }
}