/**
 * @fileOverview Centralizes exports for all custom hooks in the application
 * 
 * This file serves as the entry point for importing hooks, allowing for cleaner imports:
 * import { useBlackjack, useStrategyHint } from '@/hooks';
 */

export { useBlackjack } from './useBlackjack';
export { useStrategyHint } from './useStrategyHint';
export { usePerformanceMetrics } from './usePerformanceMetrics';
export { useGameSettings } from './useGameSettings';
export { useIsMobile } from './use-mobile';
export { useToast, toast } from './use-toast';

// Re-export types that might be needed by hook consumers
export type { Toast } from './use-toast';