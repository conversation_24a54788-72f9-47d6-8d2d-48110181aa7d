# Copilot Instructions for Stack Advantage (BJTrainer)

## Goal
Maintain well-documented, modular, and production-ready code for the Stack Advantage Blackjack card counting trainer, ensuring adherence to the project blueprint and tech stack using <PERSON><PERSON><PERSON>'s context-aware suggestions.

## 🔄 File Update Rules
When Copilot writes new components, hooks, utility functions, or core game logic:

- Ensure JSDoc or TSDoc comments are added for clarity.
- If significant features or architectural changes are made, consider suggesting updates to relevant sections in `/docs/blueprint.md`.

**Copilot Rule:**
**When:** New code is generated (component, hook, function, core logic)
**Then:** Add appropriate documentation (TSDoc/JSDoc) and consider suggesting updates to `/docs/blueprint.md` for major changes.

## 🧱 Tech Stack
Use only the following technologies unless directed otherwise:

- **Framework:** Next.js (App Router)
- **Language:** TypeScript
- **UI Library:** React
- **Styling:** Tailwind CSS, shadcn/ui components
- **State Management:** (Specify if decided, e.g., React Context, Zustand, Redux)
- **Linting/Formatting:** E<PERSON><PERSON>, Prettier (as configured in `package.json`)

Copilot should prefer Next.js conventions, React functional components with Hooks, and Tailwind CSS utility classes. Leverage shadcn/ui components where appropriate.

## 🧠 Code Architecture
Follow clean code principles:

- **Modularity:** Keep components, hooks, and utilities focused on a single responsibility. Organize files logically within the `src/` directory (`components/`, `hooks/`, `lib/`, `app/`, `ai/`).
- **Type Safety:** Utilize TypeScript effectively for strong typing and interfaces.
- **Readability:** Write clear, concise, and well-commented code.
- **Component Structure:** Prefer functional components and hooks. Use shadcn/ui components for the UI layer.
- **AI Logic:** Keep AI-related logic separated within the `src/ai/` directory.

## 🔀 Branching Strategy (Example - Adapt if needed)
**Branches:**

- `main`: Production-ready code only.
- `dev`: Active development, staging for `main`.
- `feature/*`: Short-lived branches for specific features or bug fixes.

**Rules:**

- All merges to `main` must go through `dev`.
- No direct commits to `main`.
- Feature branches should be merged to `dev` only after testing and review.

## 🚀 CI/CD Rules (If applicable)
Maintain any existing GitHub Actions CI/CD setup:

- Every push to `dev` or `main` should trigger linting, type checking, and build validation.
- Deployments should only happen from `main` after a successful pipeline.

**Copilot Rule:**
Do not introduce changes that break existing build or deployment workflows.

## 📝 Work Item Tracking
All development tasks, features, and bugs are tracked using Jira. Please reference relevant Jira issue keys in commit messages where applicable (e.g., `feat: Implement new card dealing animation (BJ-123)`).

## ✅ End-of-Prompt Checklist
At the end of a generation:

- Did you add necessary TSDoc/JSDoc comments?
- Is the code modular and well-organized within the project structure?
- Does the code adhere to the specified Tech Stack (Next.js, TypeScript, Tailwind, shadcn/ui)?
- Does the change respect the defined branching strategy?
- If CI/CD is set up, will the pipeline likely pass?
- Were relevant sections of `/docs/blueprint.md` considered for updates if major changes were made?
