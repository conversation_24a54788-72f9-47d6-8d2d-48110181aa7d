# AI Configuration
# You need a valid Google Gemini API key for AI features to work
# Get one from https://makersuite.google.com/app/apikey
GOOGLE_GENAI_API_KEY=your_google_genai_api_key_here

# AI Model Configuration
# Available models: googleai/gemini-1.5-pro (for more advanced reasoning)
# Default is googleai/gemini-2.0-flash (faster responses)
NEXT_PUBLIC_AI_MODEL=googleai/gemini-2.0-flash

# AI Logging
AI_LOGGING=false
AI_LOG_LEVEL=info
AI_CACHING=true

# Node Environment
NODE_ENV=development
